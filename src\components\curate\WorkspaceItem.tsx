import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { GripVertical, X, Edit, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { motion } from 'framer-motion';

export interface WorkspaceItemProps {
  id: string;
  type: string;
  title: string;
  icon: React.ReactNode;
  onRemove: (id: string) => void;
  onEdit?: (id: string) => void;
}

export function WorkspaceItem({ id, type, title, icon, onRemove, onEdit }: WorkspaceItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 999 : 0,
    boxShadow: isDragging ? '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' : 'none',
  };

  return (
    <motion.div
      ref={setNodeRef}
      style={style}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'border rounded-md p-3 mb-2 bg-white flex items-center justify-between group transition-all duration-200',
        isDragging ? 'shadow-xl scale-[1.02] rotate-1 border-primary/30' : 'hover:border-primary/20 hover:shadow-md'
      )}
    >
      <div className="flex items-center gap-3 flex-1">
        <motion.div
          {...attributes}
          {...listeners}
          className="cursor-grab hover:text-primary p-1 rounded-md hover:bg-gray-100 active:cursor-grabbing"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          <GripVertical className="h-5 w-5 text-gray-400" />
        </motion.div>
        <div className="flex items-center gap-2 flex-1">
          <div className="flex-shrink-0">{icon}</div>
          <div className="flex-1">
            <div className="text-sm font-medium">{title}</div>
            <div className="text-xs text-gray-500">{type}</div>
          </div>
        </div>
      </div>
      <motion.div
        className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-all duration-200"
        initial={{ x: 20, opacity: 0 }}
        animate={{ x: 0, opacity: isDragging ? 0 : 1 }}
        transition={{ delay: 0.1 }}
      >
        {onEdit && (
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 bg-gray-50"
              onClick={() => onEdit(id)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          </motion.div>
        )}
        <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50 bg-gray-50"
            onClick={() => onRemove(id)}
          >
            <X className="h-4 w-4" />
          </Button>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
