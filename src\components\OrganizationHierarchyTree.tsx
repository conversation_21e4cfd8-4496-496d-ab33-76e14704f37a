import React from "react";
import { ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface OrganizationHierarchyTreeProps {
  selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  };
}

export function OrganizationHierarchyTree({ selection }: OrganizationHierarchyTreeProps) {
  // Check if there are any selections
  const hasSelections = 
    selection.businessUnits.length > 0 ||
    selection.departmentGroups.length > 0 ||
    selection.departments.length > 0 ||
    selection.divisions.length > 0 ||
    selection.subDivisions.length > 0 ||
    selection.categories.length > 0 ||
    selection.grades.length > 0 ||
    selection.designations.length > 0;

  if (!hasSelections) {
    return <div className="text-sm text-muted-foreground italic">No selections made</div>;
  }

  // Create a structured representation of the hierarchy
  const hierarchyItems = [];

  // For each business unit, create a hierarchy tree
  for (const businessUnit of selection.businessUnits) {
    const businessUnitItem = {
      businessUnit,
      departmentGroups: [] as Array<{
        departmentGroup: string;
        departments: Array<{
          department: string;
          divisions: Array<{
            division: string;
            subDivisions: Array<{
              subDivision: string;
              categories: Array<{
                category: string;
                grades: Array<{
                  grade: string;
                  designations: string[];
                }>;
              }>;
            }>;
          }>;
        }>;
      }>,
    };

    // Add relevant department groups for this business unit
    for (const departmentGroup of selection.departmentGroups) {
      const departmentGroupItem = {
        departmentGroup,
        departments: [] as Array<{
          department: string;
          divisions: Array<{
            division: string;
            subDivisions: Array<{
              subDivision: string;
              categories: Array<{
                category: string;
                grades: Array<{
                  grade: string;
                  designations: string[];
                }>;
              }>;
            }>;
          }>;
        }>,
      };

      // Add relevant departments for this department group
      for (const department of selection.departments) {
        const departmentItem = {
          department,
          divisions: [] as Array<{
            division: string;
            subDivisions: Array<{
              subDivision: string;
              categories: Array<{
                category: string;
                grades: Array<{
                  grade: string;
                  designations: string[];
                }>;
              }>;
            }>;
          }>,
        };

        // Add relevant divisions for this department
        for (const division of selection.divisions) {
          const divisionItem = {
            division,
            subDivisions: [] as Array<{
              subDivision: string;
              categories: Array<{
                category: string;
                grades: Array<{
                  grade: string;
                  designations: string[];
                }>;
              }>;
            }>,
          };

          // Add relevant sub-divisions for this division
          for (const subDivision of selection.subDivisions) {
            const subDivisionItem = {
              subDivision,
              categories: [] as Array<{
                category: string;
                grades: Array<{
                  grade: string;
                  designations: string[];
                }>;
              }>,
            };

            // Add relevant categories for this sub-division
            for (const category of selection.categories) {
              const categoryItem = {
                category,
                grades: [] as Array<{
                  grade: string;
                  designations: string[];
                }>,
              };

              // Add relevant grades for this category
              for (const grade of selection.grades) {
                const gradeItem = {
                  grade,
                  designations: [] as string[],
                };

                // Add relevant designations for this grade
                for (const designation of selection.designations) {
                  gradeItem.designations.push(designation);
                }

                // Only add grade if it has designations or if there are no designations selected
                if (gradeItem.designations.length > 0 || selection.designations.length === 0) {
                  categoryItem.grades.push(gradeItem);
                }
              }

              // Only add category if it has grades or if there are no grades selected
              if (categoryItem.grades.length > 0 || selection.grades.length === 0) {
                subDivisionItem.categories.push(categoryItem);
              }
            }

            // Only add sub-division if it has categories or if there are no categories selected
            if (subDivisionItem.categories.length > 0 || selection.categories.length === 0) {
              divisionItem.subDivisions.push(subDivisionItem);
            }
          }

          // Only add division if it has sub-divisions or if there are no sub-divisions selected
          if (divisionItem.subDivisions.length > 0 || selection.subDivisions.length === 0) {
            departmentItem.divisions.push(divisionItem);
          }
        }

        // Only add department if it has divisions or if there are no divisions selected
        if (departmentItem.divisions.length > 0 || selection.divisions.length === 0) {
          departmentGroupItem.departments.push(departmentItem);
        }
      }

      // Only add department group if it has departments or if there are no departments selected
      if (departmentGroupItem.departments.length > 0 || selection.departments.length === 0) {
        businessUnitItem.departmentGroups.push(departmentGroupItem);
      }
    }

    // Add the business unit to the hierarchy items
    hierarchyItems.push(businessUnitItem);
  }

  // If there are no business units selected, show a simplified view of the selections
  if (selection.businessUnits.length === 0) {
    return (
      <div className="space-y-2">
        <div className="text-sm font-medium">Selection Summary:</div>
        <div className="flex flex-wrap gap-2">
          {selection.departmentGroups.map(group => (
            <Badge key={group} variant="outline" className="bg-blue-50 text-blue-700">
              {group}
            </Badge>
          ))}
          {selection.departments.map(dept => (
            <Badge key={dept} variant="outline" className="bg-green-50 text-green-700">
              {dept}
            </Badge>
          ))}
          {selection.divisions.map(div => (
            <Badge key={div} variant="outline" className="bg-purple-50 text-purple-700">
              {div}
            </Badge>
          ))}
          {selection.subDivisions.map(subdiv => (
            <Badge key={subdiv} variant="outline" className="bg-orange-50 text-orange-700">
              {subdiv}
            </Badge>
          ))}
          {selection.categories.map(cat => (
            <Badge key={cat} variant="outline" className="bg-teal-50 text-teal-700">
              {cat}
            </Badge>
          ))}
          {selection.grades.map(grade => (
            <Badge key={grade} variant="outline" className="bg-indigo-50 text-indigo-700">
              {grade}
            </Badge>
          ))}
          {selection.designations.map(designation => (
            <Badge key={designation} variant="outline" className="bg-pink-50 text-pink-700">
              {designation}
            </Badge>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {hierarchyItems.map((item, index) => (
        <div key={index} className="border rounded-md p-3">
          <div className="text-sm font-medium mb-2">Business Unit: {item.businessUnit}</div>
          <div className="pl-4 space-y-3">
            {item.departmentGroups.map((dg, dgIndex) => (
              <div key={dgIndex}>
                <div className="flex items-center text-sm">
                  <span className="font-medium">Department Group:</span>
                  <span className="ml-2">{dg.departmentGroup}</span>
                </div>
                <div className="pl-4 space-y-2 mt-1">
                  {dg.departments.map((dept, deptIndex) => (
                    <div key={deptIndex}>
                      <div className="flex items-center text-sm">
                        <span className="font-medium">Department:</span>
                        <span className="ml-2">{dept.department}</span>
                      </div>
                      <div className="pl-4 space-y-2 mt-1">
                        {dept.divisions.map((div, divIndex) => (
                          <div key={divIndex}>
                            <div className="flex items-center text-sm">
                              <span className="font-medium">Division:</span>
                              <span className="ml-2">{div.division}</span>
                            </div>
                            <div className="pl-4 space-y-2 mt-1">
                              {div.subDivisions.map((subdiv, subdivIndex) => (
                                <div key={subdivIndex}>
                                  <div className="flex items-center text-sm">
                                    <span className="font-medium">Sub-Division:</span>
                                    <span className="ml-2">{subdiv.subDivision}</span>
                                  </div>
                                  <div className="pl-4 space-y-2 mt-1">
                                    {subdiv.categories.map((cat, catIndex) => (
                                      <div key={catIndex}>
                                        <div className="flex items-center text-sm">
                                          <span className="font-medium">Category:</span>
                                          <span className="ml-2">{cat.category}</span>
                                        </div>
                                        <div className="pl-4 space-y-2 mt-1">
                                          {cat.grades.map((grade, gradeIndex) => (
                                            <div key={gradeIndex}>
                                              <div className="flex items-center text-sm">
                                                <span className="font-medium">Grade:</span>
                                                <span className="ml-2">{grade.grade}</span>
                                              </div>
                                              <div className="pl-4 space-y-2 mt-1">
                                                {grade.designations.map((designation, desigIndex) => (
                                                  <div key={desigIndex} className="flex items-center text-sm">
                                                    <span className="font-medium">Designation:</span>
                                                    <span className="ml-2">{designation}</span>
                                                  </div>
                                                ))}
                                              </div>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
