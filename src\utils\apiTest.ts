import apiService from '@/services/apiService';
import { store } from '@/store';
import { forceStopLoading, validateTokens } from '@/store/slices/authSlice';

/**
 * Test function to verify API service is working with correct headers
 * This can be called from the browser console for testing
 */
export const testApiService = async () => {
  try {
    console.log('Testing API Service with x-enterprise-id header...');

    // Test the login config endpoint
    const config = await apiService.getLoginConfig();
    console.log('Login config fetched successfully:', config);

    return config;
  } catch (error) {
    console.error('API test failed:', error);
    throw error;
  }
};

/**
 * Debug function to check authentication state
 */
export const debugAuth = () => {
  const state = store.getState();
  console.log('Authentication State:', {
    isAuthenticated: state.auth.isAuthenticated,
    isLoading: state.auth.isLoading,
    hasTokens: !!state.auth.tokens,
    hasAccessToken: !!state.auth.tokens?.accessToken,
    hasRefreshToken: !!state.auth.tokens?.refreshToken,
    error: state.auth.error,
    loginConfig: state.auth.loginConfig,
  });

  if (state.auth.tokens?.accessToken) {
    try {
      const payload = JSON.parse(atob(state.auth.tokens.accessToken.split('.')[1]));
      const currentTime = Date.now() / 1000;
      console.log('Token Info:', {
        expires: new Date(payload.exp * 1000),
        isExpired: payload.exp < currentTime,
        timeUntilExpiry: payload.exp - currentTime,
      });
    } catch (error) {
      console.log('Token parsing error:', error);
    }
  }

  return state.auth;
};

/**
 * Force stop loading state (for debugging stuck loading)
 */
export const forceStopLoadingDebug = () => {
  console.log('Forcing stop loading state...');
  store.dispatch(forceStopLoading());
};

/**
 * Force validate tokens (for debugging)
 */
export const forceValidateTokens = () => {
  console.log('Forcing token validation...');
  store.dispatch(validateTokens());
};

// Make functions available globally for testing
if (typeof window !== 'undefined') {
  (window as any).testApiService = testApiService;
  (window as any).debugAuth = debugAuth;
  (window as any).forceStopLoading = forceStopLoadingDebug;
  (window as any).forceValidateTokens = forceValidateTokens;
}
