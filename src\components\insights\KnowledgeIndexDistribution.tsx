import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface KnowledgeIndexDistributionProps {
  selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  };
}

// Mock data for knowledge index distribution
const getKnowledgeDistribution = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return [
    { name: 'Expert', value: 20, color: '#10B981' },
    { name: 'Proficient', value: 30, color: '#3B82F6' },
    { name: 'Competent', value: 25, color: '#F59E0B' },
    { name: '<PERSON>gin<PERSON>', value: 15, color: '#EF4444' },
    { name: 'No Knowledge', value: 10, color: '#6B7280' },
  ];
};

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      className="text-xs"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const KnowledgeIndexDistribution: React.FC<KnowledgeIndexDistributionProps> = ({ selection }) => {
  const data = getKnowledgeDistribution(selection);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Knowledge Index Distribution</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={renderCustomizedLabel}
              outerRadius={100}
              fill="#8884d8"
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip formatter={(value: number) => `${value}%`} />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default KnowledgeIndexDistribution;