import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ExternalLink, FileText, Info } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

export default function H5PContent() {
  const { toast } = useToast();

  // H5P content URL
  const h5pUrl = "https://h5p.kauveryhospital.com/wp-login.php";

  // Handle opening in new tab
  const handleOpenInNewTab = () => {
    toast({
      title: "Opening H5P Content",
      description: "The H5P content is opening in a new tab.",
    });
    window.open(h5pUrl, '_blank');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-center justify-between">
        <motion.h1
          className="text-3xl font-bold tracking-tight flex items-center gap-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <FileText className="h-8 w-8 text-primary" />
          H5P Content
        </motion.h1>
        <motion.div
          className="flex items-center gap-2"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Button
            variant="default"
            size="sm"
            onClick={handleOpenInNewTab}
            className="gap-2"
          >
            <ExternalLink className="h-4 w-4" />
            Open H5P Content
          </Button>
        </motion.div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <Card className="border-2 border-primary/10 shadow-lg">
            <CardHeader>
              <div className="flex items-center gap-2">
                <div className="rounded-full bg-primary/10 p-2">
                  <FileText className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <CardTitle>H5P Interactive Content</CardTitle>
                  <CardDescription>Access your interactive H5P learning materials</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-muted/30 p-4 rounded-md border border-muted flex items-start gap-3">
                <div className="bg-blue-100 p-2 rounded-full mt-1">
                  <Info className="h-5 w-5 text-blue-600" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-medium">About H5P Content</h3>
                  <p className="text-sm text-muted-foreground">
                    H5P content will open in a new tab for the best interactive experience. This ensures that all features work correctly and you have the full screen space to engage with the content.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-medium">Benefits of H5P Content</h3>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>Interactive learning experiences</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>Engaging multimedia content</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>Self-paced learning modules</span>
                    </li>
                    <li className="flex items-center gap-2">
                      <div className="h-1.5 w-1.5 rounded-full bg-primary"></div>
                      <span>Immediate feedback on activities</span>
                    </li>
                  </ul>
                </div>

                <div className="flex flex-col items-center justify-center space-y-4 p-6 bg-muted/20 rounded-lg border border-muted">
                  <div className="rounded-full bg-primary/10 p-4">
                    <ExternalLink className="h-8 w-8 text-primary" />
                  </div>
                  <div className="text-center space-y-2">
                    <h3 className="font-medium">Ready to start learning?</h3>
                    <p className="text-sm text-muted-foreground">Click the button below to access your H5P content</p>
                  </div>
                  <Button
                    size="lg"
                    onClick={handleOpenInNewTab}
                    className="mt-2 gap-2 w-full"
                  >
                    <ExternalLink className="h-4 w-4" />
                    Open H5P Content
                  </Button>
                </div>
              </div>

              <div className="text-xs text-muted-foreground mt-4 p-2 bg-muted/30 rounded-md">
                <p>URL: <code className="bg-muted p-1 rounded">{h5pUrl}</code></p>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
