import React from "react";
import { useTheme } from "next-themes";
import { But<PERSON> } from "@/components/ui/button";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  return (
    <div className="grid grid-cols-3 gap-2">
      <Button
        variant="outline"
        className={`justify-start px-3 py-6 h-auto ${theme === 'light' ? 'border-primary ring-1 ring-primary' : ''}`}
        onClick={() => setTheme("light")}
      >
        <span className="w-4 h-4 rounded-full bg-white mr-2 border"></span>
        Light
      </Button>
      <Button
        variant="outline"
        className={`justify-start px-3 py-6 h-auto ${
          theme === 'dark'
            ? 'border-primary ring-1 ring-primary bg-zinc-900 text-white'
            : 'bg-zinc-900 text-white border-zinc-800'
        }`}
        onClick={() => setTheme("dark")}
      >
        <span className="w-4 h-4 rounded-full bg-zinc-700 mr-2 border border-zinc-600"></span>
        Dark
      </Button>
      <Button
        variant="outline"
        className={`justify-start px-3 py-6 h-auto ${
          theme === 'system'
            ? 'border-primary ring-1 ring-primary'
            : ''
        }`}
        onClick={() => setTheme("system")}
      >
        <span className="w-4 h-4 rounded-full bg-gradient-to-r from-white to-zinc-800 mr-2 border"></span>
        System
      </Button>
    </div>
  );
}
