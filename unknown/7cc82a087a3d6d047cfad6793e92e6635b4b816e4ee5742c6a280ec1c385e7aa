import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { motion } from "framer-motion";
import {
  Search,
  ArrowLeft,
  Eye,
  Users,
  Filter,
  Download,
  CheckSquare,
} from "lucide-react";
import { ViewAssignmentDialog } from "@/components/ViewAssignmentDialog";
import { TableColumnFilter } from "@/components/TableColumnFilter";
import { UserSelectionDialog } from "@/components/UserSelectionDialog";
import { toast } from "@/hooks/use-toast";

// Checklist data structure - focused on checklists from CFD hierarchy
const checklistAreas = [
  "Patient Safety Checklist",
  "Medication Checklist", 
  "Equipment Checklist",
];

const checklistTopics = {
  "Patient Safety Checklist": [
    "Patient Safety Checklist",
  ],
  "Medication Checklist": [
    "Medication Checklist",
  ],
  "Equipment Checklist": [
    "Equipment Checklist",
  ],
};

const checklistUnits = {
  "Patient Safety Checklist": [
    "Patient Identification Verification",
    "Allergy Check",
    "Fall Risk Assessment",
    "Infection Control Measures",
    "Emergency Equipment Check",
    "Vital Signs Monitoring",
    "Pain Assessment",
    "Discharge Planning"
  ],
  "Medication Checklist": [
    "Medication Reconciliation",
    "Dosage Verification",
    "Administration Route Check",
    "Drug Interaction Review",
    "Patient Education",
    "Side Effect Monitoring",
    "Storage Requirements",
    "Expiry Date Verification"
  ],
  "Equipment Checklist": [
    "Equipment Functionality Test",
    "Safety Inspection",
    "Calibration Verification",
    "Maintenance Schedule Check",
    "User Training Verification",
    "Documentation Review",
    "Compliance Audit",
    "Emergency Procedures"
  ],
};

// Generate table data for checklists
const generateChecklistTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit: string;
  }> = [];

  Object.entries(checklistTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      const units = checklistUnits[topic] || [];
      const baseTopicId = `CHK-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`;
      const responsibleDept = area.includes("Patient") ? "Clinical Services" :
                             area.includes("Medication") ? "Pharmacy Services" : "Technical Services";
      const contentOwner = area.includes("Patient") ? "Clinical Safety Team" :
                          area.includes("Medication") ? "Pharmacy Safety Team" : "Technical Safety Team";

      // Create separate rows for each unit
      if (units.length > 0) {
        units.forEach((unit, unitIndex) => {
          data.push({
            area,
            topic,
            topicId: `${baseTopicId}-${String(unitIndex + 1).padStart(2, '0')}`,
            version: "v3.0",
            responsibleDept,
            contentOwner,
            unit,
          });
        });
      } else {
        // If no units, create one row without unit
        data.push({
          area,
          topic,
          topicId: baseTopicId,
          version: "v3.0",
          responsibleDept,
          contentOwner,
          unit: "No units",
        });
      }
    });
  });

  return data;
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export default function ChecklistLibraryAssignment() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedArea, setSelectedArea] = useState("");
  const [viewAssignmentOpen, setViewAssignmentOpen] = useState(false);
  const [selectedAssignmentItem, setSelectedAssignmentItem] = useState<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit?: string;
  } | null>(null);

  // Column-specific filters
  const [filters, setFilters] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });

  const [selectedValues, setSelectedValues] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });

  const [openPopover, setOpenPopover] = useState<string | null>(null);

  // User assignment states
  const [userSelectionOpen, setUserSelectionOpen] = useState(false);
  const [selectedItemForAssignment, setSelectedItemForAssignment] = useState<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit: string;
  } | null>(null);

  // Track assigned users for each item
  const [assignedUsers, setAssignedUsers] = useState<Record<string, Array<{
    id: string;
    name: string;
    employeeNo: string;
    email: string;
    department: string;
    division: string;
  }>>>({});

  const tableData = generateChecklistTableData();

  // Helper function to get unique values for dropdowns
  const getUniqueValues = (field: keyof typeof filters) => {
    const values = tableData.map(item => item[field]).filter(Boolean);
    return [...new Set(values)].sort();
  };

  // Helper functions for filter management
  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    if (value) {
      setSelectedValues(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handleDropdownSelect = (field: string, value: string) => {
    if (value === "all") {
      setSelectedValues(prev => ({ ...prev, [field]: "" }));
    } else {
      setSelectedValues(prev => ({ ...prev, [field]: value }));
    }
    setFilters(prev => ({ ...prev, [field]: "" }));
  };

  const clearFilter = (field: string) => {
    setFilters(prev => ({ ...prev, [field]: "" }));
    setSelectedValues(prev => ({ ...prev, [field]: "" }));
  };

  const clearAllFilters = () => {
    setFilters({ area: "", topic: "", unit: "", topicId: "", version: "", responsibleDept: "", contentOwner: "" });
    setSelectedValues({ area: "", topic: "", unit: "", topicId: "", version: "", responsibleDept: "", contentOwner: "" });
    setSelectedArea("");
    setSearchTerm("");
    setOpenPopover(null);
  };

  // Handle assignment functionality
  const handleAssignUsers = (item: any) => {
    setSelectedItemForAssignment(item);
    setUserSelectionOpen(true);
  };

  const handleUserAssignment = async (selectedUsers: any[]) => {
    if (!selectedItemForAssignment) return;

    const itemKey = selectedItemForAssignment.topicId;

    // Get existing assigned users for this item
    const existingUsers = assignedUsers[itemKey] || [];

    // Create new user objects from selected users
    const newUsers = selectedUsers.map(user => ({
      id: user.id,
      name: user.name,
      employeeNo: user.employeeNo,
      email: user.email,
      department: user.department,
      division: user.division,
    }));

    // Filter out users that are already assigned (based on ID)
    const existingUserIds = new Set(existingUsers.map(user => user.id));
    const usersToAdd = newUsers.filter(user => !existingUserIds.has(user.id));
    const alreadyAssignedCount = selectedUsers.length - usersToAdd.length;

    // Combine existing users with new users (cumulative assignment)
    const updatedUsers = [...existingUsers, ...usersToAdd];

    // Update assigned users for this item
    setAssignedUsers(prev => ({
      ...prev,
      [itemKey]: updatedUsers
    }));

    // Show appropriate feedback message
    if (usersToAdd.length > 0 && alreadyAssignedCount > 0) {
      toast({
        title: "Assignment updated",
        description: `Added ${usersToAdd.length} new user(s). ${alreadyAssignedCount} user(s) were already assigned.`,
      });
    } else if (usersToAdd.length > 0) {
      toast({
        title: "Assignment successful",
        description: `Successfully assigned ${usersToAdd.length} user(s) to ${selectedItemForAssignment.topic}.`,
      });
    } else {
      toast({
        title: "No new assignments",
        description: "All selected users were already assigned to this item.",
        variant: "default",
      });
    }
  };

  // Get assigned users for display
  const getAssignedUsersDisplay = (topicId: string) => {
    const users = assignedUsers[topicId] || [];
    if (users.length === 0) return "No assignments";
    if (users.length === 1) return users[0].name;
    return `${users[0].name} +${users.length - 1} more`;
  };

  // Enhanced filtering logic
  const filteredData = tableData.filter((item) => {
    const matchesSearch = searchTerm === "" ||
      item.area.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.topic.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.unit.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.topicId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.responsibleDept.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.contentOwner.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesArea = selectedArea === "" || item.area === selectedArea;

    // Column-specific filters
    const matchesFilters = Object.entries(filters).every(([key, filterValue]) => {
      if (!filterValue && !selectedValues[key as keyof typeof selectedValues]) return true;
      const itemValue = item[key as keyof typeof item];
      if (selectedValues[key as keyof typeof selectedValues]) {
        return itemValue === selectedValues[key as keyof typeof selectedValues];
      }
      return itemValue.toLowerCase().includes(filterValue.toLowerCase());
    });

    return matchesSearch && matchesArea && matchesFilters;
  });

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/employee-training-assignment')}
            className="gap-2"
          >
            <ArrowLeft size={16} />
            Back to Employee Training Assignment
          </Button>
        </div>
        
        <h1 className="text-3xl font-bold tracking-tight mb-2">
          Checklist Library & Assignment
        </h1>
        <p className="text-muted-foreground">
          Browse and assign checklists from the library organized by checklist categories
        </p>
      </motion.div>

      <motion.div variants={itemVariants}>
        <Card className="shadow-sm border-slate-200">
          <div className="p-6">
            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search checklist areas, topics, or IDs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={selectedArea}
                onChange={(e) => setSelectedArea(e.target.value)}
                className="px-3 py-2 border border-input bg-background rounded-md text-sm"
              >
                <option value="">All Checklist Areas</option>
                {checklistAreas.map((area) => (
                  <option key={area} value={area}>
                    {area}
                  </option>
                ))}
              </select>

              <Button
                variant="outline"
                onClick={clearAllFilters}
                className="gap-2"
                disabled={!Object.values(filters).some(v => v !== "") && !Object.values(selectedValues).some(v => v !== "")}
              >
                Clear All Filters
              </Button>

              <Button variant="outline" className="gap-2">
                <Download size={16} />
                Export
              </Button>
            </div>

            {/* Results Summary */}
            <div className="mb-4 text-sm text-muted-foreground">
              Showing {filteredData.length} of {tableData.length} checklists
              {searchTerm && ` for "${searchTerm}"`}
              {selectedArea && ` in ${selectedArea}`}
            </div>

            {/* Checklist Library Table */}
            <div className="border rounded-lg overflow-hidden">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableHead className="font-medium">
                      <TableColumnFilter
                        columnName="Area"
                        fieldKey="area"
                        uniqueValues={getUniqueValues('area')}
                        filterValue={filters.area}
                        selectedValue={selectedValues.area}
                        isOpen={openPopover === 'area'}
                        onOpenChange={(open) => setOpenPopover(open ? 'area' : null)}
                        onFilterChange={handleFilterChange}
                        onDropdownSelect={handleDropdownSelect}
                        onClearFilter={clearFilter}
                      />
                    </TableHead>
                    <TableHead className="font-medium">
                      <TableColumnFilter
                        columnName="Topic"
                        fieldKey="topic"
                        uniqueValues={getUniqueValues('topic')}
                        filterValue={filters.topic}
                        selectedValue={selectedValues.topic}
                        isOpen={openPopover === 'topic'}
                        onOpenChange={(open) => setOpenPopover(open ? 'topic' : null)}
                        onFilterChange={handleFilterChange}
                        onDropdownSelect={handleDropdownSelect}
                        onClearFilter={clearFilter}
                      />
                    </TableHead>
                    <TableHead className="font-medium">
                      <TableColumnFilter
                        columnName="Unit"
                        fieldKey="unit"
                        uniqueValues={getUniqueValues('unit')}
                        filterValue={filters.unit}
                        selectedValue={selectedValues.unit}
                        isOpen={openPopover === 'unit'}
                        onOpenChange={(open) => setOpenPopover(open ? 'unit' : null)}
                        onFilterChange={handleFilterChange}
                        onDropdownSelect={handleDropdownSelect}
                        onClearFilter={clearFilter}
                      />
                    </TableHead>
                    <TableHead className="font-medium">
                      <TableColumnFilter
                        columnName="Topic ID"
                        fieldKey="topicId"
                        uniqueValues={getUniqueValues('topicId')}
                        filterValue={filters.topicId}
                        selectedValue={selectedValues.topicId}
                        isOpen={openPopover === 'topicId'}
                        onOpenChange={(open) => setOpenPopover(open ? 'topicId' : null)}
                        onFilterChange={handleFilterChange}
                        onDropdownSelect={handleDropdownSelect}
                        onClearFilter={clearFilter}
                      />
                    </TableHead>
                    <TableHead className="font-medium">
                      <TableColumnFilter
                        columnName="Version"
                        fieldKey="version"
                        uniqueValues={getUniqueValues('version')}
                        filterValue={filters.version}
                        selectedValue={selectedValues.version}
                        isOpen={openPopover === 'version'}
                        onOpenChange={(open) => setOpenPopover(open ? 'version' : null)}
                        onFilterChange={handleFilterChange}
                        onDropdownSelect={handleDropdownSelect}
                        onClearFilter={clearFilter}
                      />
                    </TableHead>
                    <TableHead className="font-medium">
                      <TableColumnFilter
                        columnName="Responsible Dept."
                        fieldKey="responsibleDept"
                        uniqueValues={getUniqueValues('responsibleDept')}
                        filterValue={filters.responsibleDept}
                        selectedValue={selectedValues.responsibleDept}
                        isOpen={openPopover === 'responsibleDept'}
                        onOpenChange={(open) => setOpenPopover(open ? 'responsibleDept' : null)}
                        onFilterChange={handleFilterChange}
                        onDropdownSelect={handleDropdownSelect}
                        onClearFilter={clearFilter}
                      />
                    </TableHead>
                    <TableHead className="font-medium">
                      <TableColumnFilter
                        columnName="Content Owner"
                        fieldKey="contentOwner"
                        uniqueValues={getUniqueValues('contentOwner')}
                        filterValue={filters.contentOwner}
                        selectedValue={selectedValues.contentOwner}
                        isOpen={openPopover === 'contentOwner'}
                        onOpenChange={(open) => setOpenPopover(open ? 'contentOwner' : null)}
                        onFilterChange={handleFilterChange}
                        onDropdownSelect={handleDropdownSelect}
                        onClearFilter={clearFilter}
                      />
                    </TableHead>
                    <TableHead className="font-medium">Assigned Employee</TableHead>
                    <TableHead className="font-medium text-center">Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.length > 0 ? (
                    filteredData.map((item, index) => (
                      <TableRow key={index} className="hover:bg-muted/50">
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <CheckSquare size={16} className="text-orange-600" />
                            {item.area}
                          </div>
                        </TableCell>
                        <TableCell className="font-medium">{item.topic}</TableCell>
                        <TableCell className="text-sm text-muted-foreground">
                          {item.unit}
                        </TableCell>
                        <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                        <TableCell>{item.version}</TableCell>
                        <TableCell>{item.responsibleDept}</TableCell>
                        <TableCell>{item.contentOwner}</TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {getAssignedUsersDisplay(item.topicId)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2 justify-center">
                            <Button
                              size="sm"
                              className="gap-1 bg-purple-600 hover:bg-purple-700"
                              onClick={() => handleAssignUsers(item)}
                            >
                              <Users size={14} />
                              Assign/Passage
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="gap-1"
                              onClick={() => {
                                setSelectedAssignmentItem(item);
                                setViewAssignmentOpen(true);
                              }}
                            >
                              <Eye size={14} />
                              View Assignment
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Filter className="h-8 w-8 mb-2 opacity-40" />
                          <p className="text-sm font-medium">No checklists found</p>
                          <p className="text-xs mt-1">
                            Try adjusting your search or filter criteria
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </Card>
      </motion.div>

      {/* View Assignment Dialog */}
      <ViewAssignmentDialog
        open={viewAssignmentOpen}
        onOpenChange={setViewAssignmentOpen}
        assignmentItem={selectedAssignmentItem}
        assignedUsers={selectedAssignmentItem ? assignedUsers[selectedAssignmentItem.topicId] || [] : []}
      />

      {/* User Selection Dialog */}
      <UserSelectionDialog
        open={userSelectionOpen}
        onOpenChange={setUserSelectionOpen}
        onAssign={handleUserAssignment}
        title="Assign Users to Checklist"
        assignmentItem={selectedItemForAssignment}
      />
    </motion.div>
  );
}
