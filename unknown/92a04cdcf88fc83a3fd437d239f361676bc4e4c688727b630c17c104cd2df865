import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/hooks/use-toast";
import { Filter, X, FilterX, Download, FileDown } from "lucide-react";
import * as XLSX from "xlsx";
import { generateTrainingAssignmentPDF } from "@/lib/pdf-utils";

interface ViewAssignmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  assignmentItem: {
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit?: string;
  } | null;
  assignedUsers?: Array<{
    id: string;
    name: string;
    employeeNo: string;
    email: string;
    department: string;
    division: string;
  }>;
}

// Mock assignment data structure similar to the View Learning Assignment page
const mockAssignmentData = {
  id: "1",
  employeeIds: ["1", "2"],
  employeeName: "2 Employees",
  trainingProgram: "",
  businessUnit: "KCN, KTN",
  departmentGroup: "Clinical Services, Emergency Services",
  department: "Nursing, Intensive Care",
  division: "Patient Care, Specialized Nursing",
  subDivision: "Intensive Care, General Care",
  category: "Clinical, Critical Care",
  grade: "Junior, Mid-level",
  designation: "Nurse Assistant, Junior Technician",
  status: "Not Started",
  assignmentStatus: "Active" as const,
  assignedDate: "2024-01-15",
  dueDate: "2024-02-15",
  notes: "Assignment for selected content.",
};

export function ViewAssignmentDialog({
  open,
  onOpenChange,
  assignmentItem,
  assignedUsers = [],
}: ViewAssignmentDialogProps) {
  // State for filters
  const [filters, setFilters] = useState({
    businessUnit: "",
    departmentGroup: "",
    department: "",
    division: "",
    subDivision: "",
    category: "",
    grade: "",
    designation: "",
    assignedEmployee: "",
  });

  // State to track which filter popover is open
  const [openPopover, setOpenPopover] = useState<string | null>(null);

  // State to track selected values from dropdowns
  const [selectedValues, setSelectedValues] = useState({
    businessUnit: "",
    departmentGroup: "",
    department: "",
    division: "",
    subDivision: "",
    category: "",
    grade: "",
    designation: "",
    assignedEmployee: "",
  });

  // State for filtered rows
  const [filteredRows, setFilteredRows] = useState<Array<{
    businessUnit: string;
    departmentGroup: string;
    department: string;
    division: string;
    subDivision: string;
    category: string;
    grade: string;
    designation: string;
    employeeCount: number;
    assignedEmployee: string;
    status: string;
    dueDate: string;
  }>>([]);

  // Function to generate static random employee count based on a seed
  const generateStaticRandomEmployeeCount = (seed: string) => {
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
      const char = seed.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash % 15) + 1;
  };

  // Generate table rows based on assignment data with real assigned users
  const generateTableRows = () => {
    if (!assignmentItem) return [];

    const assignment = {
      ...mockAssignmentData,
      trainingProgram: `${assignmentItem.area} → ${assignmentItem.topic}${assignmentItem.unit ? ` → ${assignmentItem.unit}` : ''}`,
    };

    const businessUnits = assignment.businessUnit.split(", ");
    const departmentGroups = assignment.departmentGroup.split(", ");
    const departments = assignment.department.split(", ");
    const divisions = assignment.division.split(", ");
    const subDivisions = assignment.subDivision.split(", ");
    const categories = assignment.category.split(", ");
    const grades = assignment.grade.split(", ");
    const designations = assignment.designation.split(", ");

    const rows: Array<{
      businessUnit: string;
      departmentGroup: string;
      department: string;
      division: string;
      subDivision: string;
      category: string;
      grade: string;
      designation: string;
      employeeCount: number;
      assignedEmployee: string;
      status: string;
      dueDate: string;
    }> = [];

    businessUnits.forEach((businessUnit) => {
      departmentGroups.forEach((departmentGroup) => {
        departments.forEach((department) => {
          divisions.forEach((division) => {
            subDivisions.forEach((subDivision) => {
              categories.forEach((category) => {
                grades.forEach((grade) => {
                  designations.forEach((designation) => {
                    const seed = `${businessUnit}-${departmentGroup}-${department}-${division}-${subDivision}-${category}-${grade}-${designation}`;
                    let employeeCount = generateStaticRandomEmployeeCount(seed);
                    let assignedEmployeeText = `${employeeCount} Employee${employeeCount !== 1 ? 's' : ''}`;

                    // If we have real assigned users, add them to the count and display
                    if (assignedUsers.length > 0) {
                      // For the first few rows, distribute the assigned users
                      // This ensures they appear in the assignment details even if organizational structure doesn't perfectly match
                      const isFirstRow = rows.length === 0;
                      const shouldShowAssignedUsers = isFirstRow || (
                        // Try to match based on department or division
                        assignedUsers.some(user =>
                          user.department.toLowerCase().includes(department.toLowerCase()) ||
                          user.division.toLowerCase().includes(division.toLowerCase()) ||
                          department.toLowerCase().includes(user.department.toLowerCase()) ||
                          division.toLowerCase().includes(user.division.toLowerCase())
                        )
                      );

                      if (shouldShowAssignedUsers) {
                        // For first row, show all assigned users; for others, show matching ones
                        const usersToShow = isFirstRow ? assignedUsers : assignedUsers.filter(user => {
                          return user.department.toLowerCase().includes(department.toLowerCase()) ||
                                 user.division.toLowerCase().includes(division.toLowerCase()) ||
                                 department.toLowerCase().includes(user.department.toLowerCase()) ||
                                 division.toLowerCase().includes(user.division.toLowerCase());
                        });

                        if (usersToShow.length > 0) {
                          // Add the real assigned users to the existing count
                          const totalCount = employeeCount + usersToShow.length;
                          const userNames = usersToShow.length <= 3
                            ? usersToShow.map(u => u.name).join(", ")
                            : `${usersToShow.slice(0, 2).map(u => u.name).join(", ")} +${usersToShow.length - 2} more`;
                          assignedEmployeeText = `${totalCount} Employee${totalCount !== 1 ? 's' : ''} (${usersToShow.length} newly assigned: ${userNames})`;
                          employeeCount = totalCount;
                        }
                      }
                    }

                    const row = {
                      businessUnit: businessUnit.trim(),
                      departmentGroup: departmentGroup.trim(),
                      department: department.trim(),
                      division: division.trim(),
                      subDivision: subDivision.trim(),
                      category: category.trim(),
                      grade: grade.trim(),
                      designation: designation.trim(),
                      employeeCount: employeeCount,
                      assignedEmployee: assignedEmployeeText,
                      status: assignment.status,
                      dueDate: assignment.dueDate || "N/A",
                    };
                    rows.push(row);
                  });
                });
              });
            });
          });
        });
      });
    });

    return rows;
  };

  const tableRows = generateTableRows();

  // Get all unique values for each column from the table rows
  const getUniqueValues = (field: string, rows: typeof tableRows) => {
    const values = rows.map((row) => row[field as keyof typeof row]);
    const uniqueValues = [...new Set(values)].filter(Boolean);
    return uniqueValues as string[];
  };

  // Apply filters to rows
  useEffect(() => {
    let result = [...tableRows];

    // Apply each filter - either from dropdown or search input
    if (selectedValues.businessUnit) {
      result = result.filter(
        (row) => row.businessUnit === selectedValues.businessUnit
      );
    } else if (filters.businessUnit) {
      result = result.filter((row) =>
        row.businessUnit
          .toLowerCase()
          .includes(filters.businessUnit.toLowerCase())
      );
    }

    if (selectedValues.departmentGroup) {
      result = result.filter(
        (row) => row.departmentGroup === selectedValues.departmentGroup
      );
    } else if (filters.departmentGroup) {
      result = result.filter((row) =>
        row.departmentGroup
          .toLowerCase()
          .includes(filters.departmentGroup.toLowerCase())
      );
    }

    if (selectedValues.department) {
      result = result.filter(
        (row) => row.department === selectedValues.department
      );
    } else if (filters.department) {
      result = result.filter((row) =>
        row.department
          .toLowerCase()
          .includes(filters.department.toLowerCase())
      );
    }

    if (selectedValues.division) {
      result = result.filter(
        (row) => row.division === selectedValues.division
      );
    } else if (filters.division) {
      result = result.filter((row) =>
        row.division
          .toLowerCase()
          .includes(filters.division.toLowerCase())
      );
    }

    if (selectedValues.subDivision) {
      result = result.filter(
        (row) => row.subDivision === selectedValues.subDivision
      );
    } else if (filters.subDivision) {
      result = result.filter((row) =>
        row.subDivision
          .toLowerCase()
          .includes(filters.subDivision.toLowerCase())
      );
    }

    if (selectedValues.category) {
      result = result.filter(
        (row) => row.category === selectedValues.category
      );
    } else if (filters.category) {
      result = result.filter((row) =>
        row.category
          .toLowerCase()
          .includes(filters.category.toLowerCase())
      );
    }

    if (selectedValues.grade) {
      result = result.filter((row) => row.grade === selectedValues.grade);
    } else if (filters.grade) {
      result = result.filter((row) =>
        row.grade.toLowerCase().includes(filters.grade.toLowerCase())
      );
    }

    if (selectedValues.designation) {
      result = result.filter(
        (row) => row.designation === selectedValues.designation
      );
    } else if (filters.designation) {
      result = result.filter((row) =>
        row.designation
          .toLowerCase()
          .includes(filters.designation.toLowerCase())
      );
    }

    if (selectedValues.assignedEmployee) {
      result = result.filter(
        (row) => row.assignedEmployee === selectedValues.assignedEmployee
      );
    } else if (filters.assignedEmployee) {
      result = result.filter((row) =>
        row.assignedEmployee
          .toLowerCase()
          .includes(filters.assignedEmployee.toLowerCase())
      );
    }

    setFilteredRows(result);
  }, [tableRows, filters, selectedValues]);

  // Function to handle filter changes
  const handleFilterChange = (field: string, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear dropdown selection when using text filter
    if (value) {
      setSelectedValues((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // Function to handle dropdown selection
  const handleDropdownSelect = (field: string, value: string) => {
    // If "all" is selected, clear the filter
    if (value === "all") {
      setSelectedValues((prev) => ({
        ...prev,
        [field]: "",
      }));
    } else {
      setSelectedValues((prev) => ({
        ...prev,
        [field]: value,
      }));
    }

    // Clear the text filter when using dropdown
    setFilters((prev) => ({
      ...prev,
      [field]: "",
    }));
  };

  // Function to clear a specific filter
  const clearFilter = (field: string) => {
    setFilters((prev) => ({
      ...prev,
      [field]: "",
    }));
    setSelectedValues((prev) => ({
      ...prev,
      [field]: "",
    }));
  };

  // Function to clear all filters
  const clearAllFilters = () => {
    setFilters({
      businessUnit: "",
      departmentGroup: "",
      department: "",
      division: "",
      subDivision: "",
      category: "",
      grade: "",
      designation: "",
      assignedEmployee: "",
    });
    setSelectedValues({
      businessUnit: "",
      departmentGroup: "",
      department: "",
      division: "",
      subDivision: "",
      category: "",
      grade: "",
      designation: "",
      assignedEmployee: "",
    });
  };

  // Check if any filters are active
  const hasActiveFilters = Object.values(filters).some(filter => filter !== "") ||
                          Object.values(selectedValues).some(value => value !== "");

  // State for employee details dialog
  const [employeeDetailsOpen, setEmployeeDetailsOpen] = useState(false);
  const [selectedEmployeeDetails, setSelectedEmployeeDetails] = useState<{
    count: number;
    organizationInfo: {
      businessUnit: string;
      departmentGroup: string;
      department: string;
      division: string;
      subDivision: string;
      category: string;
      grade: string;
      designation: string;
    };
    users: Array<{
      id: string;
      name: string;
      employeeNo: string;
      email: string;
      department: string;
      division: string;
      status: string;
      assignedDate: string;
      dueDate: string;
    }>;
  } | null>(null);

  // Function to generate mock user data
  const generateMockUserData = (count: number, organizationInfo: {
    businessUnit: string;
    departmentGroup: string;
    department: string;
    division: string;
    subDivision: string;
    category: string;
    grade: string;
    designation: string;
  }) => {
    const firstNames = [
      "John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa",
      "James", "Maria", "William", "Jennifer", "Richard", "Patricia", "Charles",
      "Linda", "Joseph", "Barbara", "Thomas", "Elizabeth", "Christopher", "Susan",
      "Daniel", "Jessica", "Matthew", "Karen", "Anthony", "Nancy", "Mark", "Betty"
    ];

    const lastNames = [
      "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
      "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson",
      "Thomas", "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson",
      "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson"
    ];

    const statuses = ["Active", "In Progress", "Completed", "Pending"];

    return Array.from({ length: count }, (_, index) => {
      const firstName = firstNames[index % firstNames.length];
      const lastName = lastNames[(index + 5) % lastNames.length];
      const employeeNo = `EMP${String(1000 + index).padStart(4, '0')}`;

      return {
        id: `user-${index + 1}`,
        name: `${firstName} ${lastName}`,
        employeeNo: employeeNo,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@koach.com`,
        department: organizationInfo.department,
        division: organizationInfo.division,
        status: statuses[index % statuses.length],
        assignedDate: "2024-01-15",
        dueDate: "2024-02-15",
      };
    });
  };

  // Function to handle employee count click
  const handleEmployeeCountClick = (row: {
    employeeCount: number;
    businessUnit: string;
    departmentGroup: string;
    department: string;
    division: string;
    subDivision: string;
    category: string;
    grade: string;
    designation: string;
  }) => {
    // Generate mock users for the original count
    const originalCount = generateStaticRandomEmployeeCount(`${row.businessUnit}-${row.departmentGroup}-${row.department}-${row.division}-${row.subDivision}-${row.category}-${row.grade}-${row.designation}`);
    const mockUsers = generateMockUserData(originalCount, {
      businessUnit: row.businessUnit,
      departmentGroup: row.departmentGroup,
      department: row.department,
      division: row.division,
      subDivision: row.subDivision,
      category: row.category,
      grade: row.grade,
      designation: row.designation
    });

    // Filter real assigned users that match this row's organizational structure
    // If this is the first organizational group or if users match the structure, include them
    const matchingAssignedUsers = assignedUsers.filter(user => {
      // More flexible matching - include users if there's any organizational overlap
      return user.department.toLowerCase().includes(row.department.toLowerCase()) ||
             user.division.toLowerCase().includes(row.division.toLowerCase()) ||
             row.department.toLowerCase().includes(user.department.toLowerCase()) ||
             row.division.toLowerCase().includes(user.division.toLowerCase()) ||
             // Also include if this is a primary organizational category
             row.department === "Nursing" || row.department === "Intensive Care" ||
             row.departmentGroup === "Clinical Services";
    });

    // Convert real assigned users to the expected format
    const realUsersForDetails = matchingAssignedUsers.map(user => ({
      id: user.id,
      name: user.name,
      employeeNo: user.employeeNo,
      email: user.email,
      department: user.department,
      division: user.division,
      status: "Active (Newly Assigned)",
      assignedDate: "2024-01-15",
      dueDate: "2024-02-15",
    }));

    // Combine mock users with real assigned users
    const allUsers = [...mockUsers, ...realUsersForDetails];

    setSelectedEmployeeDetails({
      count: row.employeeCount,
      organizationInfo: {
        businessUnit: row.businessUnit,
        departmentGroup: row.departmentGroup,
        department: row.department,
        division: row.division,
        subDivision: row.subDivision,
        category: row.category,
        grade: row.grade,
        designation: row.designation
      },
      users: allUsers
    });

    setEmployeeDetailsOpen(true);
  };

  // Function to download employee details as Excel
  const downloadEmployeeDetailsExcel = () => {
    if (!selectedEmployeeDetails) return;

    try {
      const exportData = selectedEmployeeDetails.users.map((user) => ({
        "Employee No": user.employeeNo,
        "Name": user.name,
        "Email": user.email,
        "Department": user.department,
        "Division": user.division,
        "Status": user.status,
        "Assigned Date": user.assignedDate,
        "Due Date": user.dueDate,
        "Business Unit": selectedEmployeeDetails.organizationInfo.businessUnit,
        "Department Group": selectedEmployeeDetails.organizationInfo.departmentGroup,
        "Sub-Division": selectedEmployeeDetails.organizationInfo.subDivision,
        "Category": selectedEmployeeDetails.organizationInfo.category,
        "Grade": selectedEmployeeDetails.organizationInfo.grade,
        "Designation": selectedEmployeeDetails.organizationInfo.designation
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Employee Details");

      const fileName = `Employee_Details_${selectedEmployeeDetails.organizationInfo.businessUnit}_${selectedEmployeeDetails.organizationInfo.departmentGroup}_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      toast({
        title: "Download Complete",
        description: `Employee details have been downloaded as ${fileName}`,
      });
    } catch (error) {
      console.error("Error downloading employee details:", error);
      toast({
        title: "Error",
        description: "There was an error downloading the employee details. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Function to download employee details as PDF
  const downloadEmployeeDetailsPDF = () => {
    if (!selectedEmployeeDetails || !assignmentItem) return;

    try {
      // Create a mock assignment object for PDF generation
      const mockAssignment = {
        id: assignmentItem.topicId,
        employeeIds: selectedEmployeeDetails.users.map(u => u.id),
        employeeName: `${selectedEmployeeDetails.count} Employee${selectedEmployeeDetails.count !== 1 ? 's' : ''}`,
        trainingProgram: `${assignmentItem.area} → ${assignmentItem.topic}${assignmentItem.unit ? ` → ${assignmentItem.unit}` : ''}`,
        businessUnit: selectedEmployeeDetails.organizationInfo.businessUnit,
        departmentGroup: selectedEmployeeDetails.organizationInfo.departmentGroup,
        department: selectedEmployeeDetails.organizationInfo.department,
        division: selectedEmployeeDetails.organizationInfo.division,
        subDivision: selectedEmployeeDetails.organizationInfo.subDivision,
        category: selectedEmployeeDetails.organizationInfo.category,
        grade: selectedEmployeeDetails.organizationInfo.grade,
        designation: selectedEmployeeDetails.organizationInfo.designation,
        status: "Active",
        assignmentStatus: "Active" as const,
        assignedDate: "2024-01-15",
        dueDate: "2024-02-15",
        notes: `Assignment details for ${assignmentItem.area} → ${assignmentItem.topic}`,
      };

      // Create unit hierarchy
      const unitHierarchy = [{
        area: assignmentItem.area,
        topic: assignmentItem.topic,
        unit: assignmentItem.unit || assignmentItem.topic,
      }];

      // Generate PDF
      generateTrainingAssignmentPDF(
        mockAssignment,
        selectedEmployeeDetails.users,
        [assignmentItem.unit || assignmentItem.topic],
        unitHierarchy
      );

      toast({
        title: "PDF Generated",
        description: "Employee details have been downloaded as a PDF.",
      });
    } catch (error) {
      console.error("Error generating PDF:", error);
      toast({
        title: "Error",
        description: "There was an error generating the PDF. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Assignment Details
          </DialogTitle>
          <DialogDescription>
            {assignmentItem && (
              <div className="text-sm text-muted-foreground mt-2">
                <div className="font-medium">
                  {assignmentItem.area} → {assignmentItem.topic}
                  {assignmentItem.unit && ` → ${assignmentItem.unit}`}
                </div>
                <div className="mt-1">
                  ID: {assignmentItem.topicId} | Version: {assignmentItem.version} | 
                  Owner: {assignmentItem.contentOwner}
                </div>
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-auto">
          <div className="space-y-4">
            {/* Filter Controls */}
            <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium text-slate-700">
                  Showing {filteredRows.length} of {tableRows.length} assignments
                </span>
                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearAllFilters}
                    className="gap-2 text-xs"
                  >
                    <FilterX size={14} />
                    Clear All Filters
                  </Button>
                )}
              </div>
            </div>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                    <TableHead className="font-medium text-left sticky left-0 z-10 bg-slate-50">
                      <div className="flex items-center gap-1">
                        <span>Business Unit</span>
                        <Popover
                          open={openPopover === "businessUnit"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "businessUnit" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.businessUnit ||
                                  selectedValues.businessUnit
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[280px]">
                            <div className="space-y-4">
                              <h4 className="text-sm font-medium">
                                Filter Business Unit
                              </h4>

                              <div className="space-y-3">
                                <div>
                                  <label className="text-xs font-medium text-muted-foreground">
                                    Select from dropdown
                                  </label>
                                  <Select
                                    value={selectedValues.businessUnit}
                                    onValueChange={(value) =>
                                      handleDropdownSelect(
                                        "businessUnit",
                                        value
                                      )
                                    }
                                  >
                                    <SelectTrigger className="h-9 mt-1">
                                      <SelectValue placeholder="Select business unit" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="all">All</SelectItem>
                                      {getUniqueValues("businessUnit", tableRows).map(
                                        (value) => (
                                          <SelectItem key={value} value={value}>
                                            {value}
                                          </SelectItem>
                                        )
                                      )}
                                    </SelectContent>
                                  </Select>
                                </div>

                                <div className="text-center text-xs text-muted-foreground">
                                  OR
                                </div>

                                <div>
                                  <label className="text-xs font-medium text-muted-foreground">
                                    Search by text
                                  </label>
                                  <Input
                                    placeholder="Type to search..."
                                    value={filters.businessUnit}
                                    onChange={(e) =>
                                      handleFilterChange(
                                        "businessUnit",
                                        e.target.value
                                      )
                                    }
                                    className="h-9 mt-1"
                                  />
                                </div>
                              </div>

                              <div className="flex justify-between pt-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("businessUnit")}
                                  className="text-xs px-4"
                                  disabled={
                                    !filters.businessUnit &&
                                    !selectedValues.businessUnit
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs px-4 bg-purple-600 hover:bg-purple-700"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>

                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Department Group</span>
                        <Popover
                          open={openPopover === "departmentGroup"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "departmentGroup" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.departmentGroup ||
                                  selectedValues.departmentGroup
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Department Group
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("departmentGroup")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.departmentGroup}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "departmentGroup",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.departmentGroup}
                                  onValueChange={(value) =>
                                    handleDropdownSelect(
                                      "departmentGroup",
                                      value
                                    )
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues(
                                      "departmentGroup",
                                      tableRows
                                    ).map((value) => (
                                      <SelectItem key={value} value={value}>
                                        {value}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("departmentGroup")}
                                  className="text-xs"
                                  disabled={
                                    !filters.departmentGroup &&
                                    !selectedValues.departmentGroup
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>

                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Department</span>
                        <Popover
                          open={openPopover === "department"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "department" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.department ||
                                  selectedValues.department
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Department
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("department")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.department}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "department",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.department}
                                  onValueChange={(value) =>
                                    handleDropdownSelect("department", value)
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues("department", tableRows).map(
                                      (value) => (
                                        <SelectItem key={value} value={value}>
                                          {value}
                                        </SelectItem>
                                      )
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("department")}
                                  className="text-xs"
                                  disabled={
                                    !filters.department &&
                                    !selectedValues.department
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>

                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Division</span>
                        <Popover
                          open={openPopover === "division"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "division" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.division ||
                                  selectedValues.division
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Division
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("division")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.division}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "division",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.division}
                                  onValueChange={(value) =>
                                    handleDropdownSelect("division", value)
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues("division", tableRows).map(
                                      (value) => (
                                        <SelectItem key={value} value={value}>
                                          {value}
                                        </SelectItem>
                                      )
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("division")}
                                  className="text-xs"
                                  disabled={
                                    !filters.division &&
                                    !selectedValues.division
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>

                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Sub-Division</span>
                        <Popover
                          open={openPopover === "subDivision"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "subDivision" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.subDivision ||
                                  selectedValues.subDivision
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Sub-Division
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("subDivision")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.subDivision}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "subDivision",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.subDivision}
                                  onValueChange={(value) =>
                                    handleDropdownSelect("subDivision", value)
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues("subDivision", tableRows).map(
                                      (value) => (
                                        <SelectItem key={value} value={value}>
                                          {value}
                                        </SelectItem>
                                      )
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("subDivision")}
                                  className="text-xs"
                                  disabled={
                                    !filters.subDivision &&
                                    !selectedValues.subDivision
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>
                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Category</span>
                        <Popover
                          open={openPopover === "category"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "category" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.category ||
                                  selectedValues.category
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Category
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("category")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.category}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "category",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.category}
                                  onValueChange={(value) =>
                                    handleDropdownSelect("category", value)
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues("category", tableRows).map(
                                      (value) => (
                                        <SelectItem key={value} value={value}>
                                          {value}
                                        </SelectItem>
                                      )
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("category")}
                                  className="text-xs"
                                  disabled={
                                    !filters.category &&
                                    !selectedValues.category
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>

                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Grade</span>
                        <Popover
                          open={openPopover === "grade"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "grade" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.grade ||
                                  selectedValues.grade
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Grade
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("grade")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.grade}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "grade",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.grade}
                                  onValueChange={(value) =>
                                    handleDropdownSelect("grade", value)
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues("grade", tableRows).map(
                                      (value) => (
                                        <SelectItem key={value} value={value}>
                                          {value}
                                        </SelectItem>
                                      )
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("grade")}
                                  className="text-xs"
                                  disabled={
                                    !filters.grade &&
                                    !selectedValues.grade
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>

                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Designation</span>
                        <Popover
                          open={openPopover === "designation"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "designation" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.designation ||
                                  selectedValues.designation
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Designation
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("designation")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.designation}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "designation",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.designation}
                                  onValueChange={(value) =>
                                    handleDropdownSelect("designation", value)
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues("designation", tableRows).map(
                                      (value) => (
                                        <SelectItem key={value} value={value}>
                                          {value}
                                        </SelectItem>
                                      )
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("designation")}
                                  className="text-xs"
                                  disabled={
                                    !filters.designation &&
                                    !selectedValues.designation
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>

                    <TableHead className="font-medium text-left">
                      <div className="flex items-center gap-1">
                        <span>Assigned Employee</span>
                        <Popover
                          open={openPopover === "assignedEmployee"}
                          onOpenChange={(open) =>
                            setOpenPopover(open ? "assignedEmployee" : null)
                          }
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 p-0"
                            >
                              <Filter
                                size={12}
                                className={`${
                                  filters.assignedEmployee ||
                                  selectedValues.assignedEmployee
                                    ? "text-primary"
                                    : "text-muted-foreground"
                                }`}
                              />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent align="start" className="w-[250px]">
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h4 className="text-sm font-medium">
                                  Filter Assigned Employee
                                </h4>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => clearFilter("assignedEmployee")}
                                  className="h-6 w-6 p-0"
                                >
                                  <X size={12} />
                                </Button>
                              </div>

                              <div className="space-y-2">
                                <Input
                                  placeholder="Search..."
                                  value={filters.assignedEmployee}
                                  onChange={(e) =>
                                    handleFilterChange(
                                      "assignedEmployee",
                                      e.target.value
                                    )
                                  }
                                  className="h-8"
                                />

                                <Select
                                  value={selectedValues.assignedEmployee}
                                  onValueChange={(value) =>
                                    handleDropdownSelect("assignedEmployee", value)
                                  }
                                >
                                  <SelectTrigger className="h-8">
                                    <SelectValue placeholder="Select option" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="all">All</SelectItem>
                                    {getUniqueValues("assignedEmployee", tableRows).map(
                                      (value) => (
                                        <SelectItem key={value} value={value}>
                                          {value}
                                        </SelectItem>
                                      )
                                    )}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div className="flex justify-between">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => clearFilter("assignedEmployee")}
                                  className="text-xs"
                                  disabled={
                                    !filters.assignedEmployee &&
                                    !selectedValues.assignedEmployee
                                  }
                                >
                                  Clear
                                </Button>
                                <Button
                                  size="sm"
                                  onClick={() => setOpenPopover(null)}
                                  className="text-xs"
                                >
                                  Apply
                                </Button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      </div>
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRows.length > 0 ? (
                    filteredRows.map((row, index) => (
                      <TableRow key={index} className="hover:bg-muted/50">
                        <TableCell className="font-medium">{row.businessUnit}</TableCell>
                        <TableCell>{row.departmentGroup}</TableCell>
                        <TableCell>{row.department}</TableCell>
                        <TableCell>{row.division}</TableCell>
                        <TableCell>{row.subDivision}</TableCell>
                        <TableCell>{row.category}</TableCell>
                        <TableCell>{row.grade}</TableCell>
                        <TableCell>{row.designation}</TableCell>
                        <TableCell>
                          <Button
                            variant="link"
                            className="p-0 h-auto text-blue-600 hover:text-blue-800"
                            onClick={() => handleEmployeeCountClick(row)}
                          >
                            {row.assignedEmployee}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} className="h-24 text-center">
                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                          <Filter className="h-8 w-8 mb-2 opacity-40" />
                          <p className="text-sm font-medium">No assignments found</p>
                          <p className="text-xs mt-1">
                            Try adjusting your filter criteria
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </DialogContent>

      {/* Employee Details Dialog */}
      <Dialog open={employeeDetailsOpen} onOpenChange={setEmployeeDetailsOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] border-muted/40 shadow-lg flex flex-col">
          <DialogHeader className="bg-muted/10 pb-2">
            <DialogTitle className="text-xl font-semibold">
              Employee Details
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              {selectedEmployeeDetails && (
                <>
                  Showing {selectedEmployeeDetails.count} employee{selectedEmployeeDetails.count !== 1 ? 's' : ''} for {selectedEmployeeDetails.organizationInfo.businessUnit} - {selectedEmployeeDetails.organizationInfo.departmentGroup}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-auto">
            {selectedEmployeeDetails && (
              <div className="space-y-6">
                {/* Organization Information */}
                <div className="bg-muted/20 rounded-lg p-4">
                  <h3 className="text-sm font-semibold text-primary/90 mb-3">
                    Organization Details
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground block">Business Unit</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.businessUnit}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Department Group</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.departmentGroup}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Department</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.department}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Division</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.division}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Sub-Division</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.subDivision}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Category</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.category}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Grade</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.grade}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground block">Designation</span>
                      <span className="font-medium">{selectedEmployeeDetails.organizationInfo.designation}</span>
                    </div>
                  </div>
                </div>

                {/* Employee List */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold text-primary/90">
                      Employee List ({selectedEmployeeDetails.count} employee{selectedEmployeeDetails.count !== 1 ? 's' : ''})
                    </h3>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={downloadEmployeeDetailsExcel}
                        className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                      >
                        <Download size={16} />
                        Download Excel
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={downloadEmployeeDetailsPDF}
                        className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                      >
                        <FileDown size={16} />
                        Download PDF
                      </Button>
                    </div>
                  </div>

                  <div className="border rounded-lg overflow-hidden">
                    <Table>
                      <TableHeader className="bg-muted/30">
                        <TableRow>
                          <TableHead className="font-medium">Employee No</TableHead>
                          <TableHead className="font-medium">Name</TableHead>
                          <TableHead className="font-medium">Email</TableHead>
                          <TableHead className="font-medium">Department</TableHead>
                          <TableHead className="font-medium">Division</TableHead>
                          <TableHead className="font-medium">Status</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {selectedEmployeeDetails.users.map((user, index) => (
                          <TableRow key={user.id} className="hover:bg-muted/50">
                            <TableCell className="font-mono text-sm">{user.employeeNo}</TableCell>
                            <TableCell className="font-medium">{user.name}</TableCell>
                            <TableCell className="text-sm text-muted-foreground">{user.email}</TableCell>
                            <TableCell>{user.department}</TableCell>
                            <TableCell>{user.division}</TableCell>
                            <TableCell>
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                user.status === 'Active' ? 'bg-green-100 text-green-800' :
                                user.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                                user.status === 'Completed' ? 'bg-purple-100 text-purple-800' :
                                'bg-yellow-100 text-yellow-800'
                              }`}>
                                {user.status}
                              </span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
