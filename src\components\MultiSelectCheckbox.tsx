import React from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface MultiSelectCheckboxProps {
  title: string;
  options: string[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  disabled?: boolean;
  maxHeight?: string;
  className?: string;
}

export function MultiSelectCheckbox({
  title,
  options,
  selectedValues,
  onChange,
  disabled = false,
  maxHeight = "200px",
  className
}: MultiSelectCheckboxProps) {
  const allSelected = options.length > 0 && selectedValues.length === options.length;

  const handleSelectAll = () => {
    onChange([...options]);
  };

  const handleClearAll = () => {
    onChange([]);
  };

  const handleToggleItem = (item: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedValues, item]);
    } else {
      onChange(selectedValues.filter(i => i !== item));
    }
  };

  return (
    <div className={`${disabled ? 'opacity-50 pointer-events-none' : ''}`}>
      <Accordion type="single" collapsible defaultValue={title} className={`border rounded-md bg-muted/5 ${className || ''}`}>
        <AccordionItem value={title} className="border-0">
          <AccordionTrigger className="px-3 py-2 hover:no-underline hover:bg-gray-100 focus:bg-gray-100" style={{ backgroundColor: "#f9fafb" }}>
            <div className="flex items-center justify-between w-full">
              <h4 className="text-sm font-medium">{title}</h4>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-3 pt-3 pb-3">
            <div className={`border rounded-md p-3 bg-background overflow-y-auto`} style={{ maxHeight }}>
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id={`select-all-${title}`}
                    checked={allSelected}
                    onChange={(e) => {
                      if (e.target.checked) {
                        handleSelectAll();
                      } else {
                        handleClearAll();
                      }
                    }}
                    className="h-4 w-4 rounded border-gray-300"
                    disabled={disabled || options.length === 0}
                  />
                  <Label htmlFor={`select-all-${title}`} className="text-sm font-medium">
                    Select all {title.toLowerCase()}
                  </Label>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleClearAll}
                  disabled={disabled || selectedValues.length === 0}
                  className="h-7 text-xs"
                >
                  Clear All
                </Button>
              </div>

              <div className="space-y-2">
                {options.map((option) => (
                  <div key={option} className="flex items-center space-x-2 py-1 px-2 hover:bg-muted/20 rounded-md">
                    <input
                      type="checkbox"
                      id={`${title}-${option}`}
                      checked={selectedValues.includes(option)}
                      onChange={(e) => handleToggleItem(option, e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300"
                      disabled={disabled}
                    />
                    <Label htmlFor={`${title}-${option}`} className="text-sm cursor-pointer">{option}</Label>
                  </div>
                ))}
                {options.length === 0 && (
                  <div className="text-sm text-muted-foreground py-1 px-2">
                    No options available
                  </div>
                )}
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Display selected items without accordion */}
      {selectedValues.length > 0 && (
        <div className={`border rounded-md mt-2 ${className || ''}`}>
          {/* Header without background */}
          <div className="px-3 py-2">
            <div className="flex items-center justify-between w-full">
              <h4 className="text-sm font-medium">
                Selected {title}
                <span className="text-xs text-muted-foreground ml-2">
                  {selectedValues.length} selected
                </span>
              </h4>
            </div>
          </div>
          {/* Content */}
          <div className="px-3 pt-0 pb-3">
            <div className="flex flex-wrap gap-1 mt-2">
              {selectedValues.map((value) => (
                <div
                  key={value}
                  className="flex items-center bg-primary/10 text-primary rounded-full px-2 py-1 text-xs"
                >
                  <span className="mr-1">{value}</span>
                  <button
                    type="button"
                    onClick={() => onChange(selectedValues.filter(v => v !== value))}
                    className="text-primary hover:text-primary/80 focus:outline-none"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M18 6 6 18"></path>
                      <path d="m6 6 12 12"></path>
                    </svg>
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
