import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface UserPerformanceMetricsProps {
  selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  };
}

// Mock data for user performance metrics
const getUserPerformanceData = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return {
    topUsers: [
      { name: '<PERSON>', score: 95 },
      { name: '<PERSON>', score: 92 },
      { name: '<PERSON>', score: 90 },
      { name: '<PERSON>', score: 88 },
      { name: '<PERSON>', score: 85 },
    ],
    bottomUsers: [
      { name: '<PERSON>', score: 45 },
      { name: '<PERSON>', score: 48 },
      { name: '<PERSON>', score: 52 },
      { name: '<PERSON>', score: 55 },
      { name: '<PERSON>', score: 58 },
    ],
    groupPerformance: [
      { name: 'Engineering', score: 82 },
      { name: 'Marketing', score: 75 },
      { name: 'Sales', score: 70 },
      { name: 'Product', score: 85 },
      { name: 'Customer Support', score: 78 },
    ],
  };
};

const UserPerformanceMetrics: React.FC<UserPerformanceMetricsProps> = ({ selection }) => {
  const [activeTab, setActiveTab] = useState('topUsers');
  const performanceData = getUserPerformanceData(selection);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>User Performance Metrics</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="topUsers" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="topUsers">Top Performers</TabsTrigger>
            <TabsTrigger value="bottomUsers">Bottom Performers</TabsTrigger>
            <TabsTrigger value="groupPerformance">Group Performance</TabsTrigger>
          </TabsList>
          <TabsContent value="topUsers" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={performanceData.topUsers}
                layout="vertical"
                margin={{ top: 20, right: 30, left: 60, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                <XAxis type="number" domain={[0, 100]} />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip formatter={(value) => [`${value}%`, 'Knowledge Score']} />
                <Legend />
                <Bar 
                  dataKey="score" 
                  name="Knowledge Score" 
                  fill="#10B981" 
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="bottomUsers" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={performanceData.bottomUsers}
                layout="vertical"
                margin={{ top: 20, right: 30, left: 60, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
                <XAxis type="number" domain={[0, 100]} />
                <YAxis dataKey="name" type="category" width={100} />
                <Tooltip formatter={(value) => [`${value}%`, 'Knowledge Score']} />
                <Legend />
                <Bar 
                  dataKey="score" 
                  name="Knowledge Score" 
                  fill="#EF4444" 
                  radius={[0, 4, 4, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="groupPerformance" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={performanceData.groupPerformance}
                margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="name" />
                <YAxis domain={[0, 100]} />
                <Tooltip formatter={(value) => [`${value}%`, 'Knowledge Score']} />
                <Legend />
                <Bar 
                  dataKey="score" 
                  name="Knowledge Score" 
                  fill="#3B82F6" 
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default UserPerformanceMetrics;