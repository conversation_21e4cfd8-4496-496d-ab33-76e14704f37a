import React from 'react';
import { X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SelectedItemTagProps {
  label: string;
  onRemove?: () => void;
  className?: string;
}

const SelectedItemTag = ({
  label,
  onRemove,
  className,
}: SelectedItemTagProps) => {
  return (
    <div className={cn(
      "inline-flex items-center gap-1 bg-gray-200 text-sm px-2 py-1 rounded-md mr-2 mb-2",
      className
    )}>
      <span>{label}</span>
      {onRemove && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            onRemove();
          }}
          className="text-muted-foreground hover:text-foreground"
        >
          <X className="h-3 w-3" />
        </button>
      )}
    </div>
  );
};

export default SelectedItemTag;
