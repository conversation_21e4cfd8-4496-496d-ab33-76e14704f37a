import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>,
  Line,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer
} from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from 'framer-motion';

interface DataSeries {
  name: string;
  dataKey: string;
  color: string;
}

interface AnimatedLineChartProps {
  title: string;
  data: any[];
  xAxisKey: string;
  series: DataSeries[];
  height?: number;
  showLegend?: boolean;
}

const AnimatedLineChart: React.FC<AnimatedLineChartProps> = ({
  title,
  data,
  xAxisKey,
  series,
  height = 300,
  showLegend = true
}) => {
  const [chartData, setChartData] = useState<any[]>([]);
  
  // Animate the data on mount
  useEffect(() => {
    // Start with empty values
    const initialData = data.map(item => {
      const newItem = { ...item };
      series.forEach(s => {
        newItem[s.dataKey] = 0;
      });
      return newItem;
    });
    
    setChartData(initialData);
    
    // Animate to actual values
    const timer = setTimeout(() => {
      setChartData(data);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [data, series]);
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="overflow-hidden">
        <CardHeader className="pb-2">
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <CardTitle>{title}</CardTitle>
          </motion.div>
        </CardHeader>
        <CardContent className={`h-[${height}px]`}>
          <ResponsiveContainer width="100%" height={height}>
            <LineChart
              data={chartData}
              margin={{
                top: 20, right: 30, left: 20, bottom: 5,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis 
                dataKey={xAxisKey} 
                axisLine={{ stroke: '#E5E7EB' }}
                tickLine={false}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
              />
              <Tooltip 
                contentStyle={{ 
                  borderRadius: '8px', 
                  border: 'none', 
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  padding: '12px'
                }}
              />
              {showLegend && <Legend />}
              
              {series.map((s, index) => (
                <Line
                  key={s.dataKey}
                  type="monotone"
                  dataKey={s.dataKey}
                  name={s.name}
                  stroke={s.color}
                  strokeWidth={2}
                  dot={{ r: 0 }}
                  activeDot={{ r: 6 }}
                  animationDuration={1500}
                  animationEasing="ease-in-out"
                  animationBegin={index * 200}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AnimatedLineChart;
