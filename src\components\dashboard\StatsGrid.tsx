import React from 'react';
import { motion } from 'framer-motion';
import StatCard from './StatCard';
import { LucideIcon } from 'lucide-react';

interface StatItem {
  title: string;
  count: number | string;
  icon: LucideIcon;
  tooltipText?: string;
  className?: string;
  iconColor?: string;
  trend?: number;
  trendLabel?: string;
}

interface StatsGridProps {
  stats: StatItem[];
  columns?: 2 | 3 | 4;
}

const StatsGrid: React.FC<StatsGridProps> = ({ 
  stats,
  columns = 4
}) => {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };
  
  const getGridCols = () => {
    switch (columns) {
      case 2: return 'grid-cols-1 md:grid-cols-2';
      case 3: return 'grid-cols-1 md:grid-cols-3';
      case 4: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
    }
  };
  
  return (
    <motion.div
      className={`grid ${getGridCols()} gap-6`}
      variants={container}
      initial="hidden"
      animate="show"
    >
      {stats.map((stat, index) => (
        <motion.div key={index} variants={item}>
          <StatCard
            title={stat.title}
            count={stat.count}
            icon={stat.icon}
            tooltipText={stat.tooltipText}
            className={stat.className}
            iconColor={stat.iconColor}
            trend={stat.trend}
            trendLabel={stat.trendLabel}
          />
        </motion.div>
      ))}
    </motion.div>
  );
};

export default StatsGrid;
