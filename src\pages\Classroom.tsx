
import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table";
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { format } from "date-fns";
import { CalendarIcon, Download, Edit, Eye, FileIcon, MoreHorizontal, Search, Star, Trash2, X } from "lucide-react";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";

// Schema for training program form
const trainingProgramSchema = z.object({
  name: z.string().min(1, { message: "Training program name is required" }),
  code: z.string().min(1, { message: "Training program code is required" }),
  overview: z.string().min(1, { message: "Overview is required" }),
  duration: z.coerce.number().min(1, { message: "Duration must be at least 1 hour" }),
  targetAudience: z.string().min(1, { message: "Target audience is required" }),
  clinicalType: z.string().min(1, { message: "Clinical type is required" }),
  mandatoryType: z.string().min(1, { message: "Mandatory type is required" }),
  notificationType: z.enum(["daily", "weekly", "monthly"]),
  reportEmails: z.string().min(1, { message: "At least one email is required" }),
});

// Schema for coordinator assignment form
const coordinatorAssignmentSchema = z.object({
  coordinatorId: z.string().min(1, { message: "Coordinator is required" }),
  units: z.array(z.string()).min(1, { message: "At least one unit must be selected" }),
});

// Mock data for training programs
const mockTrainingPrograms = [
  {
    id: "1",
    name: "Advanced Nursing Care",
    code: "ANC-001",
    certificate: "Yes",
    clinicalType: "Clinical",
    mandatory: "Yes",
    subType: "Specialty",
    targetAudience: "Registered Nurses",
    attachment: "nursing-care.pdf",
    createdBy: "John Smith",
    createdDate: "2024-04-01",
    updatedBy: "Mary Johnson",
    updatedDate: "2024-04-03",
    status: "Active",
  },
  {
    id: "2",
    name: "Emergency Response Protocol",
    code: "ERP-002",
    certificate: "Yes",
    clinicalType: "Clinical",
    mandatory: "Yes",
    subType: "Core",
    targetAudience: "All Medical Staff",
    attachment: "emergency-protocol.pdf",
    createdBy: "Robert Wilson",
    createdDate: "2024-03-15",
    updatedBy: "Sarah Davis",
    updatedDate: "2024-03-28",
    status: "Active",
  },
  {
    id: "3",
    name: "Patient Care Documentation",
    code: "PCD-003",
    certificate: "No",
    clinicalType: "Administrative",
    mandatory: "No",
    subType: "Support",
    targetAudience: "Clinical Support Staff",
    attachment: "documentation-guide.pdf",
    createdBy: "Emily Brown",
    createdDate: "2024-02-20",
    updatedBy: "Emily Brown",
    updatedDate: "2024-02-20",
    status: "Inactive",
  },
];

// Mock data for training coordinators
const mockCoordinators = [
  {
    id: "1",
    name: "Jennifer Adams",
    employeeNumber: "EMP-1001",
    division: "Nursing Education",
    department: "Staff Development",
    location: "Main Campus",
    status: "Active",
    assignedUnits: ["Medical-Surgical Unit", "Emergency Department"],
  },
  {
    id: "2",
    name: "Michael Roberts",
    employeeNumber: "EMP-1052",
    division: "Clinical Education",
    department: "Training",
    location: "North Wing",
    status: "Active",
    assignedUnits: ["Intensive Care Unit"],
  },
  {
    id: "3",
    name: "Patricia Stevens",
    employeeNumber: "EMP-2103",
    division: "Leadership Development",
    department: "Professional Growth",
    location: "South Campus",
    status: "Inactive",
    assignedUnits: ["Administrative Staff", "Management Team"],
  },
];

// Mock data for units
const mockUnits = [
  { id: "1", name: "Medical-Surgical Unit" },
  { id: "2", name: "Emergency Department" },
  { id: "3", name: "Intensive Care Unit" },
  { id: "4", name: "Pediatrics" },
  { id: "5", name: "Obstetrics" },
  { id: "6", name: "Operating Room" },
  { id: "7", name: "Recovery Room" },
  { id: "8", name: "Rehabilitation" },
  { id: "9", name: "Psychiatry" },
  { id: "10", name: "Administrative Staff" },
  { id: "11", name: "Management Team" },
];

export default function Classroom() {
  // State for tab selection
  const [activeTab, setActiveTab] = useState("program");
  
  // State for dialogs
  const [programDialogOpen, setProgramDialogOpen] = useState(false);
  const [coordinatorDialogOpen, setCoordinatorDialogOpen] = useState(false);
  
  // State for date filters
  const [dateFrom, setDateFrom] = useState<Date>();
  const [dateTo, setDateTo] = useState<Date>();
  
  // State for pagination
  const [entriesPerPage, setEntriesPerPage] = useState("10");
  const [currentPage, setCurrentPage] = useState(1);
  
  // State for search
  const [searchQuery, setSearchQuery] = useState("");
  
  // Initialize forms
  const programForm = useForm<z.infer<typeof trainingProgramSchema>>({
    resolver: zodResolver(trainingProgramSchema),
    defaultValues: {
      name: "",
      code: "",
      overview: "",
      duration: 1,
      targetAudience: "",
      clinicalType: "",
      mandatoryType: "",
      notificationType: "daily",
      reportEmails: "",
    },
  });
  
  const coordinatorForm = useForm<z.infer<typeof coordinatorAssignmentSchema>>({
    resolver: zodResolver(coordinatorAssignmentSchema),
    defaultValues: {
      coordinatorId: "",
      units: [],
    },
  });
  
  // Form submission handlers
  const onSubmitProgram = (data: z.infer<typeof trainingProgramSchema>) => {
    console.log("Training program form data:", data);
    toast.success("Training program created successfully!");
    programForm.reset();
    setProgramDialogOpen(false);
  };
  
  const onSubmitCoordinator = (data: z.infer<typeof coordinatorAssignmentSchema>) => {
    console.log("Coordinator assignment form data:", data);
    toast.success("Training coordinator assigned successfully!");
    coordinatorForm.reset();
    setCoordinatorDialogOpen(false);
  };
  
  // Helper function to filter programs by search query
  const filterPrograms = () => {
    return mockTrainingPrograms.filter(program => 
      program.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      program.code.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };
  
  // Helper function to filter coordinators by search query
  const filterCoordinators = () => {
    return mockCoordinators.filter(coordinator => 
      coordinator.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      coordinator.employeeNumber.toLowerCase().includes(searchQuery.toLowerCase())
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Classroom</h1>
      </div>

      <Card className="p-6">
        <Tabs defaultValue="program" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="program">Training Program Master</TabsTrigger>
            <TabsTrigger value="coordinator">Training Co-Ordinator Assignment</TabsTrigger>
          </TabsList>
          
          {/* Training Program Master Tab */}
          <TabsContent value="program" className="space-y-4">
            <div className="flex flex-col md:flex-row justify-between gap-4 mt-4">
              <div className="flex flex-col md:flex-row gap-2">
                <Button onClick={() => setProgramDialogOpen(true)}>Add Program</Button>
                <Button variant="outline">Export</Button>
              </div>
              
              <div className="flex flex-col md:flex-row gap-2">
                {/* Date range filter */}
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[150px] justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateFrom ? format(dateFrom, "dd/MM/yyyy") : "From Date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateFrom}
                        onSelect={setDateFrom}
                        initialFocus
                        className={cn("p-3 pointer-events-auto")}
                      />
                    </PopoverContent>
                  </Popover>
                  
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[150px] justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateTo ? format(dateTo, "dd/MM/yyyy") : "To Date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateTo}
                        onSelect={setDateTo}
                        initialFocus
                        className={cn("p-3 pointer-events-auto")}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search Programs..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            {/* Training Programs Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Training Program Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Certificate</TableHead>
                    <TableHead>Clinical Type</TableHead>
                    <TableHead>Mandatory</TableHead>
                    <TableHead>Sub-type</TableHead>
                    <TableHead>Target Audience</TableHead>
                    <TableHead>Attachment</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterPrograms().map((program) => (
                    <TableRow key={program.id}>
                      <TableCell className="font-medium">{program.name}</TableCell>
                      <TableCell>{program.code}</TableCell>
                      <TableCell>{program.certificate}</TableCell>
                      <TableCell>{program.clinicalType}</TableCell>
                      <TableCell>{program.mandatory}</TableCell>
                      <TableCell>{program.subType}</TableCell>
                      <TableCell>{program.targetAudience}</TableCell>
                      <TableCell>
                        <Button variant="ghost" size="icon">
                          <FileIcon className="h-4 w-4" />
                        </Button>
                      </TableCell>
                      <TableCell>
                        <Badge variant={program.status === "Active" ? "default" : "secondary"}>
                          {program.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Star className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <X className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {/* Pagination */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Show</span>
                <Select
                  value={entriesPerPage}
                  onValueChange={setEntriesPerPage}
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="10" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">entries</span>
              </div>
              
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious href="#" />
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink href="#" isActive>1</PaginationLink>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink href="#">2</PaginationLink>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink href="#">3</PaginationLink>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationNext href="#" />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </TabsContent>
          
          {/* Training Co-Ordinator Assignment Tab */}
          <TabsContent value="coordinator" className="space-y-4">
            <div className="flex flex-col md:flex-row justify-between gap-4 mt-4">
              <div className="flex flex-col md:flex-row gap-2">
                <Button onClick={() => setCoordinatorDialogOpen(true)}>Add Assignment</Button>
                <Button variant="outline">Export</Button>
              </div>
              
              <div className="flex flex-col md:flex-row gap-2">
                {/* Date range filter */}
                <div className="flex gap-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[150px] justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateFrom ? format(dateFrom, "dd/MM/yyyy") : "From Date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateFrom}
                        onSelect={setDateFrom}
                        initialFocus
                        className={cn("p-3 pointer-events-auto")}
                      />
                    </PopoverContent>
                  </Popover>
                  
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-[150px] justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateTo ? format(dateTo, "dd/MM/yyyy") : "To Date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={dateTo}
                        onSelect={setDateTo}
                        initialFocus
                        className={cn("p-3 pointer-events-auto")}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                
                <div className="relative">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search Coordinators..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>
            
            {/* Training Coordinators Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Coordinator Name</TableHead>
                    <TableHead>Employee Number</TableHead>
                    <TableHead>Division</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned Units</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filterCoordinators().map((coordinator) => (
                    <TableRow key={coordinator.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>{coordinator.name.substring(0, 2)}</AvatarFallback>
                          </Avatar>
                          {coordinator.name}
                        </div>
                      </TableCell>
                      <TableCell>{coordinator.employeeNumber}</TableCell>
                      <TableCell>{coordinator.division}</TableCell>
                      <TableCell>{coordinator.department}</TableCell>
                      <TableCell>{coordinator.location}</TableCell>
                      <TableCell>
                        <Badge variant={coordinator.status === "Active" ? "default" : "secondary"}>
                          {coordinator.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {coordinator.assignedUnits.map((unit, index) => (
                            <Badge key={index} variant="outline">{unit}</Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {/* Pagination */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Show</span>
                <Select
                  value={entriesPerPage}
                  onValueChange={setEntriesPerPage}
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue placeholder="10" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
                <span className="text-sm text-muted-foreground">entries</span>
              </div>
              
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious href="#" />
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink href="#" isActive>1</PaginationLink>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink href="#">2</PaginationLink>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationNext href="#" />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          </TabsContent>
        </Tabs>
      </Card>

      {/* Training Program Form Dialog */}
      <Dialog open={programDialogOpen} onOpenChange={setProgramDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add New Training Program</DialogTitle>
          </DialogHeader>
          
          <Form {...programForm}>
            <form onSubmit={programForm.handleSubmit(onSubmitProgram)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={programForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Training Program Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter program name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={programForm.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Training Program Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter program code" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormField
                control={programForm.control}
                name="overview"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Overview</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Enter program overview" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={programForm.control}
                  name="duration"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Duration (hours)</FormLabel>
                      <FormControl>
                        <Input type="number" min="1" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={programForm.control}
                  name="targetAudience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Target Audience</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter target audience" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={programForm.control}
                  name="clinicalType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Clinical Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="clinical">Clinical</SelectItem>
                          <SelectItem value="administrative">Administrative</SelectItem>
                          <SelectItem value="technical">Technical</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={programForm.control}
                  name="mandatoryType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Mandatory Type</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="mandatory">Mandatory</SelectItem>
                          <SelectItem value="optional">Optional</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <FormItem>
                <FormLabel>Attachment</FormLabel>
                <div className="mt-2">
                  <Input type="file" />
                </div>
                <FormDescription>Upload images or files related to the program</FormDescription>
              </FormItem>
              
              <FormField
                control={programForm.control}
                name="notificationType"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Module Completion Notification</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="flex flex-col space-y-1"
                      >
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="daily" id="daily" />
                          <FormLabel htmlFor="daily" className="font-normal">Daily Report</FormLabel>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="weekly" id="weekly" />
                          <FormLabel htmlFor="weekly" className="font-normal">Weekly Report</FormLabel>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="monthly" id="monthly" />
                          <FormLabel htmlFor="monthly" className="font-normal">Monthly Report</FormLabel>
                        </div>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={programForm.control}
                name="reportEmails"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Report Sending Emails</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter email addresses separated by commas" {...field} />
                    </FormControl>
                    <FormDescription>Enter multiple email addresses separated by commas</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setProgramDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Create Program</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      
      {/* Coordinator Assignment Dialog */}
      <Dialog open={coordinatorDialogOpen} onOpenChange={setCoordinatorDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Assign Training Coordinator</DialogTitle>
          </DialogHeader>
          
          <Form {...coordinatorForm}>
            <form onSubmit={coordinatorForm.handleSubmit(onSubmitCoordinator)} className="space-y-4">
              <FormField
                control={coordinatorForm.control}
                name="coordinatorId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Training Coordinator</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a coordinator" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {mockCoordinators.map(coord => (
                          <SelectItem key={coord.id} value={coord.id}>
                            {coord.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={coordinatorForm.control}
                name="units"
                render={() => (
                  <FormItem>
                    <div className="mb-4">
                      <FormLabel className="text-base">Assign Units</FormLabel>
                      <FormDescription>
                        Select units to assign to this coordinator
                      </FormDescription>
                    </div>
                    <div className="space-y-2">
                      {mockUnits.map((unit) => (
                        <div key={unit.id} className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={coordinatorForm.watch("units").includes(unit.id)}
                              onCheckedChange={(checked) => {
                                const currentUnits = coordinatorForm.getValues("units");
                                if (checked) {
                                  coordinatorForm.setValue("units", [...currentUnits, unit.id]);
                                } else {
                                  coordinatorForm.setValue(
                                    "units",
                                    currentUnits.filter((value) => value !== unit.id)
                                  );
                                }
                              }}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">
                            {unit.name}
                          </FormLabel>
                        </div>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setCoordinatorDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Save Assignment</Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
