import { useSelector, useDispatch } from 'react-redux';
import { useCallback, useEffect } from 'react';
import { RootState } from '@/store';
import { setLogout, setTokens, setLoading, setError, validateTokens } from '@/store/slices/authSlice';
import { refreshAccessToken, revokeToken } from '@/services/api';

const useAuth = () => {
  const dispatch = useDispatch();
  const { isAuthenticated, isLoading, error, tokens, loginConfig, user } = useSelector(
    (state: RootState) => state.auth
  );

  // Check if tokens are expired
  const isTokenExpired = useCallback((token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error parsing token:', error);
      return true;
    }
  }, []);

  // Refresh token if needed
  const refreshTokenIfNeeded = useCallback(async () => {
    if (!tokens?.refreshToken || !loginConfig) return;

    try {
      if (tokens.accessToken && isTokenExpired(tokens.accessToken)) {
        dispatch(setLoading(true));
        
        const cognitoDomain = localStorage.getItem('COGNITO_USER_DOMAIN') || loginConfig.COGNITO_USER_DOMAIN;
        const clientId = localStorage.getItem('COGNITO_USER_APP_CLIENT_ID') || loginConfig.COGNITO_USER_APP_CLIENT_ID;
        
        const newTokens = await refreshAccessToken(
          tokens.refreshToken,
          cognitoDomain,
          clientId
        );

        dispatch(setTokens({
          accessToken: newTokens.access_token,
          refreshToken: newTokens.refresh_token || tokens.refreshToken,
        }));
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      dispatch(setError('Session expired. Please login again.'));
      logout();
    } finally {
      dispatch(setLoading(false));
    }
  }, [tokens, loginConfig, isTokenExpired, dispatch]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      if (tokens?.accessToken && loginConfig) {
        const cognitoDomain = localStorage.getItem('COGNITO_USER_DOMAIN') || loginConfig.COGNITO_USER_DOMAIN;
        const clientId = localStorage.getItem('COGNITO_USER_APP_CLIENT_ID') || loginConfig.COGNITO_USER_APP_CLIENT_ID;
        
        await revokeToken(tokens.accessToken, cognitoDomain, clientId);
      }
    } catch (error) {
      console.error('Error during logout:', error);
    } finally {
      dispatch(setLogout());
    }
  }, [tokens, loginConfig, dispatch]);

  // Only validate tokens when they actually change, not on every render
  useEffect(() => {
    if (tokens?.accessToken) {
      // Only validate if we haven't validated recently
      const lastValidation = localStorage.getItem('lastTokenValidation');
      const now = Date.now();

      if (!lastValidation || now - parseInt(lastValidation) > 60000) { // 1 minute
        dispatch(validateTokens());
        localStorage.setItem('lastTokenValidation', now.toString());
      }
    }
  }, [tokens?.accessToken, dispatch]); // Only depend on the actual token, not the whole tokens object

  // Check authentication status on mount and token changes
  useEffect(() => {
    if (isAuthenticated && tokens?.accessToken) {
      // Set up token refresh interval
      const interval = setInterval(() => {
        refreshTokenIfNeeded();
      }, 5 * 60 * 1000); // Check every 5 minutes

      return () => clearInterval(interval);
    }
  }, [isAuthenticated, tokens, refreshTokenIfNeeded]);

  // Auto-refresh token on app focus
  useEffect(() => {
    const handleFocus = () => {
      if (isAuthenticated) {
        refreshTokenIfNeeded();
      }
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [isAuthenticated, refreshTokenIfNeeded]);

  return {
    isAuthenticated,
    isLoading,
    error,
    tokens,
    loginConfig,
    user,
    logout,
    refreshTokenIfNeeded,
  };
};

export default useAuth;
