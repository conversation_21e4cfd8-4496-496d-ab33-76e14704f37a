
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

interface KnowledgeIndexDonutProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  color?: string;
  duration?: number;
  label?: string;
}

const KnowledgeIndexDonut: React.FC<KnowledgeIndexDonutProps> = ({
  percentage,
  size = 220,
  strokeWidth = 12,
  color = '#0EA5E9',
  duration = 1.5,
  label = 'Knowledge Index'
}) => {
  const [currentPercentage, setCurrentPercentage] = useState(0);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const dash = (currentPercentage * circumference) / 100;

  // Animate the percentage on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setCurrentPercentage(percentage);
    }, 300);

    return () => clearTimeout(timer);
  }, [percentage]);

  // Calculate color based on percentage
  const getColor = () => {
    if (percentage < 40) return '#EF4444'; // Red for low
    if (percentage < 70) return '#F59E0B'; // Amber for medium
    return color; // Default color for high
  };

  const circleColor = getColor();

  return (
    <div className="relative inline-flex flex-col items-center justify-center">
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="relative"
      >
        <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`} className="transform -rotate-90">
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke="#E5E7EB"
            strokeWidth={strokeWidth}
          />

          {/* Progress circle with animation */}
          <motion.circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke={circleColor}
            strokeWidth={strokeWidth}
            strokeDasharray={circumference}
            initial={{ strokeDashoffset: circumference }}
            animate={{ strokeDashoffset: circumference - dash }}
            transition={{ duration, ease: "easeInOut" }}
            strokeLinecap="round"
          />

          {/* Add decorative dots along the circle */}
          {[0, 25, 50, 75].map((point) => {
            const angle = (point / 100) * 360 - 90;
            const x = size / 2 + (radius + strokeWidth / 2) * Math.cos((angle * Math.PI) / 180);
            const y = size / 2 + (radius + strokeWidth / 2) * Math.sin((angle * Math.PI) / 180);

            return (
              <motion.circle
                key={point}
                cx={x}
                cy={y}
                r={strokeWidth / 3}
                fill={point <= percentage ? circleColor : '#E5E7EB'}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: duration * (point / 100), duration: 0.3 }}
              />
            );
          })}
        </svg>

        {/* Percentage text with animation */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <motion.span
            className="text-5xl font-bold"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
          >
            {Math.round(currentPercentage)}%
          </motion.span>
          <motion.span
            className="text-sm text-gray-500 mt-1"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
          >
            {label}
          </motion.span>
        </div>
      </motion.div>

      {/* Legend */}
      <motion.div
        className="grid grid-cols-2 gap-4 mt-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, duration: 0.5 }}
      >
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
          <span className="text-sm">Good (70-100%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
          <span className="text-sm">Average (40-69%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
          <span className="text-sm">Poor (0-39%)</span>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-gray-300 mr-2"></div>
          <span className="text-sm">Target (100%)</span>
        </div>
      </motion.div>
    </div>
  );
};

export default KnowledgeIndexDonut;
