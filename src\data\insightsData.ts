// Mock data for the Insights page

// Knowledge Areas
export const knowledgeAreas = [
  { id: 'area1', name: 'Computer Science' },
  { id: 'area2', name: 'Business Management' },
  { id: 'area3', name: 'Medicine' },
  { id: 'area4', name: 'Engineering' },
  { id: 'area5', name: 'Arts' },
];

// Knowledge Topics by Area
export const knowledgeTopics = {
  area1: [
    { id: 'topic1', name: 'Programming' },
    { id: 'topic2', name: 'Data Science' },
    { id: 'topic3', name: 'Artificial Intelligence' },
  ],
  area2: [
    { id: 'topic4', name: 'Project Management' },
    { id: 'topic5', name: 'Finance' },
    { id: 'topic6', name: 'Marketing' },
  ],
  area3: [
    { id: 'topic7', name: 'Medical Procedures' },
    { id: 'topic8', name: 'Diagnostics' },
    { id: 'topic9', name: 'Pharmacology' },
  ],
  area4: [
    { id: 'topic10', name: 'Mechanical Engineering' },
    { id: 'topic11', name: 'Electrical Engineering' },
    { id: 'topic12', name: 'Civil Engineering' },
  ],
  area5: [
    { id: 'topic13', name: 'Visual Arts' },
    { id: 'topic14', name: 'Music' },
    { id: 'topic15', name: 'Literature' },
  ],
};

// Knowledge Units by Topic
export const knowledgeUnits = {
  topic1: [
    { id: 'unit1', name: 'Object-Oriented Programming' },
    { id: 'unit2', name: 'Functional Programming' },
    { id: 'unit3', name: 'Web Development' },
  ],
  topic2: [
    { id: 'unit4', name: 'Data Analysis' },
    { id: 'unit5', name: 'Machine Learning' },
    { id: 'unit6', name: 'Big Data' },
  ],
  topic3: [
    { id: 'unit7', name: 'Neural Networks' },
    { id: 'unit8', name: 'Natural Language Processing' },
    { id: 'unit9', name: 'Computer Vision' },
  ],
  topic4: [
    { id: 'unit10', name: 'Agile Methodology' },
    { id: 'unit11', name: 'Scrum Framework' },
    { id: 'unit12', name: 'Project Planning' },
  ],
  topic5: [
    { id: 'unit13', name: 'Financial Analysis' },
    { id: 'unit14', name: 'Investment Management' },
    { id: 'unit15', name: 'Risk Assessment' },
  ],
};

// Knowledge Index Calculation
export const calculateKnowledgeIndex = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return {
    lowRisk: 35,
    mediumRisk: 40,
    risk: 15,
    highRisk: 10,
    pullRate: 78,
  };
};

// Knowledge Distribution
export const getKnowledgeDistribution = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return [
    { name: 'Expert', value: 20, color: '#10B981' },
    { name: 'Proficient', value: 30, color: '#3B82F6' },
    { name: 'Competent', value: 25, color: '#F59E0B' },
    { name: 'Beginner', value: 15, color: '#EF4444' },
    { name: 'No Knowledge', value: 10, color: '#6B7280' },
  ];
};

// Knowledge Pull Trends
export const getKnowledgePullTrends = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return {
    area: [
      { month: 'Jan', value: 65 },
      { month: 'Feb', value: 68 },
      { month: 'Mar', value: 72 },
      { month: 'Apr', value: 75 },
      { month: 'May', value: 78 },
      { month: 'Jun', value: 82 },
    ],
    group: [
      { month: 'Jan', value: 60 },
      { month: 'Feb', value: 63 },
      { month: 'Mar', value: 67 },
      { month: 'Apr', value: 70 },
      { month: 'May', value: 73 },
      { month: 'Jun', value: 77 },
    ],
    enterprise: [
      { month: 'Jan', value: 55 },
      { month: 'Feb', value: 58 },
      { month: 'Mar', value: 62 },
      { month: 'Apr', value: 65 },
      { month: 'May', value: 68 },
      { month: 'Jun', value: 72 },
    ],
  };
};

// User Performance Metrics
export const getUserPerformanceData = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return {
    topUsers: [
      { name: 'John Doe', score: 95 },
      { name: 'Jane Smith', score: 92 },
      { name: 'Robert Johnson', score: 90 },
      { name: 'Emily Davis', score: 88 },
      { name: 'Michael Brown', score: 85 },
    ],
    bottomUsers: [
      { name: 'Thomas Wilson', score: 45 },
      { name: 'Sarah Martinez', score: 48 },
      { name: 'David Anderson', score: 52 },
      { name: 'Lisa Taylor', score: 55 },
      { name: 'James Garcia', score: 58 },
    ],
    groupPerformance: [
      { name: 'Engineering', score: 82 },
      { name: 'Marketing', score: 75 },
      { name: 'Sales', score: 70 },
      { name: 'Product', score: 85 },
      { name: 'Customer Support', score: 78 },
    ],
  };
};