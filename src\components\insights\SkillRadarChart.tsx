
import React from 'react';
import { 
  Radar, 
  RadarChart, 
  PolarGrid, 
  PolarAngleAxis, 
  PolarRadiusAxis, 
  ResponsiveContainer, 
  <PERSON>lt<PERSON>, 
  Legend 
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { skillRadarData, radarConfig } from '@/data/analyticsData';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";

const SkillRadarChart: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Skill Assessment</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ChartContainer config={radarConfig}>
          <RadarChart outerRadius={90} data={skillRadarData}>
            <PolarGrid />
            <PolarAngleAxis dataKey="subject" />
            <PolarRadiusAxis angle={30} domain={[0, 100]} />
            <Radar 
              name="user" 
              dataKey="user" 
              stroke="var(--color-user)" 
              fill="var(--color-user)" 
              fillOpacity={0.6} 
            />
            <Radar 
              name="average" 
              dataKey="average" 
              stroke="var(--color-average)" 
              fill="var(--color-average)" 
              fillOpacity={0.6} 
            />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Legend />
          </RadarChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
};

export default SkillRadarChart;
