import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  MessageSquare,
  MessageSquareDiff,
  User,
  Users,
  Building2,
  School,
  Calendar,
  Clock,
  Eye,
  Download,
  MoreHorizontal,
  Filter,
  X
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Input } from "@/components/ui/input";

// Sample data for the broadcast history
const sampleBroadcasts = [
  {
    id: "brd-001",
    title: "System Maintenance Notice",
    type: "one-way",
    targetType: "all",
    targetName: "All Users",
    sentDate: new Date(2023, 9, 15, 9, 30),
    status: "delivered",
    views: 245,
    responses: 0,
  },
  {
    id: "brd-002",
    title: "Quarterly Training Survey",
    type: "two-way",
    targetType: "department",
    targetName: "Human Resources",
    sentDate: new Date(2023, 9, 12, 14, 15),
    status: "delivered",
    views: 42,
    responses: 38,
  },
  {
    id: "brd-003",
    title: "New Product Launch",
    type: "one-way",
    targetType: "group",
    targetName: "Sales Team",
    sentDate: new Date(2023, 9, 10, 11, 0),
    status: "delivered",
    views: 78,
    responses: 0,
  },
  {
    id: "brd-004",
    title: "IT Security Quiz",
    type: "two-way",
    targetType: "class",
    targetName: "Computer Science 202",
    sentDate: new Date(2023, 9, 8, 16, 45),
    status: "delivered",
    views: 35,
    responses: 32,
  },
  {
    id: "brd-005",
    title: "Welcome Message",
    type: "one-way",
    targetType: "user",
    targetName: "Alex Johnson",
    sentDate: new Date(2023, 9, 5, 10, 20),
    status: "delivered",
    views: 1,
    responses: 0,
  },
];

// Define the filter options for each column
const filterOptions = {
  title: [], // Will be dynamically populated from data
  type: ["one-way", "two-way"],
  targetType: ["user", "group", "department", "class", "all"],
  targetName: [], // Will be dynamically populated from data
  sentDate: [], // Will be dynamically populated from data
  status: ["delivered", "pending", "failed"],
  views: [], // Will be dynamically populated from data
  responses: [], // Will be dynamically populated from data
};

export function BroadcastHistoryTable() {
  // State for broadcasts and filters
  const [broadcasts, setBroadcasts] = useState(sampleBroadcasts);
  const [filteredBroadcasts, setFilteredBroadcasts] = useState(sampleBroadcasts);

  // State to track which filter popover is open
  const [openPopover, setOpenPopover] = useState<string | null>(null);

  // State for filters
  const [filters, setFilters] = useState({
    title: "",
    type: "",
    targetType: "",
    targetName: "",
    sentDate: "",
    status: "",
    views: "",
    responses: "",
  });

  // Apply filters when they change
  useEffect(() => {
    let result = [...broadcasts];

    // Apply title filter
    if (filters.title) {
      result = result.filter(broadcast =>
        broadcast.title.toLowerCase().includes(filters.title.toLowerCase())
      );
    }

    // Apply type filter
    if (filters.type) {
      result = result.filter(broadcast => broadcast.type === filters.type);
    }

    // Apply target type filter
    if (filters.targetType) {
      result = result.filter(broadcast => broadcast.targetType === filters.targetType);
    }

    // Apply target name filter
    if (filters.targetName) {
      result = result.filter(broadcast =>
        broadcast.targetName.toLowerCase().includes(filters.targetName.toLowerCase())
      );
    }

    // Apply sent date filter
    if (filters.sentDate) {
      result = result.filter(broadcast =>
        format(broadcast.sentDate, "MMM d, yyyy").includes(filters.sentDate)
      );
    }

    // Apply status filter
    if (filters.status) {
      result = result.filter(broadcast => broadcast.status === filters.status);
    }

    // Apply views filter
    if (filters.views) {
      result = result.filter(broadcast =>
        broadcast.views.toString().includes(filters.views)
      );
    }

    // Apply responses filter
    if (filters.responses) {
      result = result.filter(broadcast =>
        broadcast.responses.toString().includes(filters.responses)
      );
    }

    setFilteredBroadcasts(result);
  }, [broadcasts, filters]);

  // Handle filter changes
  const handleFilterChange = (field: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Clear all filters
  const clearAllFilters = () => {
    setFilters({
      title: "",
      type: "",
      targetType: "",
      targetName: "",
      sentDate: "",
      status: "",
      views: "",
      responses: "",
    });
    setOpenPopover(null);
  };

  // Function to clear a specific filter
  const clearFilter = (field: string) => {
    setFilters(prev => ({
      ...prev,
      [field]: ""
    }));
  };

  // Format target name with icon
  const renderTarget = (targetType: string, targetName: string) => {
    return (
      <div className="flex items-center">
        <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center mr-2">
          {targetType === "user" && <User className="h-4 w-4 text-primary" />}
          {targetType === "group" && <Users className="h-4 w-4 text-primary" />}
          {targetType === "department" && <Building2 className="h-4 w-4 text-primary" />}
          {targetType === "class" && <School className="h-4 w-4 text-primary" />}
          {targetType === "all" && <Users className="h-4 w-4 text-primary" />}
        </div>
        <div>
          <div className="font-medium">{targetName}</div>
          <div className="text-xs text-muted-foreground capitalize">{targetType}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="border rounded-md overflow-hidden">
      <div className="flex justify-end p-2 bg-gray-50 border-b">
        <Button
          variant="outline"
          size="sm"
          onClick={clearAllFilters}
          className="text-xs"
          disabled={!Object.values(filters).some(v => v !== "")}
        >
          Clear All Filters
        </Button>
      </div>

      <Table>
        <TableHeader>
          <TableRow className="bg-primary/5">
            {/* Message Column */}
            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Message</span>
                <Popover open={openPopover === 'title'} onOpenChange={(open) => setOpenPopover(open ? 'title' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.title ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60 p-3" align="end">
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Filter Message</h4>
                      <Input
                        placeholder="Search..."
                        value={filters.title}
                        onChange={(e) => handleFilterChange('title', e.target.value)}
                        className="h-8 text-sm"
                      />
                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('title')}
                          className="text-xs"
                          disabled={!filters.title}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            {/* Type Column */}
            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Type</span>
                <Popover open={openPopover === 'type'} onOpenChange={(open) => setOpenPopover(open ? 'type' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.type ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60 p-3" align="end">
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Filter Type</h4>
                      <Select
                        value={filters.type}
                        onValueChange={(value) => handleFilterChange("type", value)}
                      >
                        <SelectTrigger className="h-8 text-sm">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Types</SelectItem>
                          <SelectItem value="one-way">One-way</SelectItem>
                          <SelectItem value="two-way">Two-way</SelectItem>
                        </SelectContent>
                      </Select>
                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('type')}
                          className="text-xs"
                          disabled={!filters.type}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            {/* Target Column */}
            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Target</span>
                <Popover open={openPopover === 'targetType'} onOpenChange={(open) => setOpenPopover(open ? 'targetType' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.targetType || filters.targetName ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-3" align="end">
                    <div className="space-y-4">
                      <h4 className="font-medium text-sm">Filter Target</h4>

                      {/* Target Type Dropdown */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Target Type</label>
                        <Select
                          value={filters.targetType}
                          onValueChange={(value) => handleFilterChange("targetType", value)}
                        >
                          <SelectTrigger className="h-8 text-sm">
                            <SelectValue placeholder="Select target type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">All Target Types</SelectItem>
                            <SelectItem value="user">Individual</SelectItem>
                            <SelectItem value="group">Group</SelectItem>
                            <SelectItem value="department">Department</SelectItem>
                            <SelectItem value="class">Class</SelectItem>
                            <SelectItem value="all">All Users</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t"></span>
                        </div>
                        <div className="relative flex justify-center text-xs">
                          <span className="bg-background px-2 text-muted-foreground">OR</span>
                        </div>
                      </div>

                      {/* Target Name Search */}
                      <div className="space-y-1">
                        <label className="text-xs font-medium">Target Name</label>
                        <Input
                          placeholder="Search target name..."
                          value={filters.targetName}
                          onChange={(e) => handleFilterChange('targetName', e.target.value)}
                          className="h-8 text-sm"
                        />
                      </div>

                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            clearFilter('targetType');
                            clearFilter('targetName');
                          }}
                          className="text-xs"
                          disabled={!filters.targetType && !filters.targetName}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            {/* Sent Date Column */}
            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Sent Date</span>
                <Popover open={openPopover === 'sentDate'} onOpenChange={(open) => setOpenPopover(open ? 'sentDate' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.sentDate ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60 p-3" align="end">
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Filter Date</h4>
                      <Input
                        placeholder="Search date..."
                        value={filters.sentDate}
                        onChange={(e) => handleFilterChange('sentDate', e.target.value)}
                        className="h-8 text-sm"
                      />
                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('sentDate')}
                          className="text-xs"
                          disabled={!filters.sentDate}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            {/* Status Column */}
            <TableHead className="font-medium">
              <div className="flex items-center justify-between">
                <span>Status</span>
                <Popover open={openPopover === 'status'} onOpenChange={(open) => setOpenPopover(open ? 'status' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6">
                      <Filter className={`h-3 w-3 ${filters.status ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60 p-3" align="end">
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Filter Status</h4>
                      <Select
                        value={filters.status}
                        onValueChange={(value) => handleFilterChange("status", value)}
                      >
                        <SelectTrigger className="h-8 text-sm">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Statuses</SelectItem>
                          <SelectItem value="delivered">Delivered</SelectItem>
                          <SelectItem value="pending">Pending</SelectItem>
                          <SelectItem value="failed">Failed</SelectItem>
                        </SelectContent>
                      </Select>
                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('status')}
                          className="text-xs"
                          disabled={!filters.status}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            {/* Views Column */}
            <TableHead className="font-medium text-right">
              <div className="flex items-center justify-end">
                <span>Views</span>
                <Popover open={openPopover === 'views'} onOpenChange={(open) => setOpenPopover(open ? 'views' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
                      <Filter className={`h-3 w-3 ${filters.views ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60 p-3" align="end">
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Filter Views</h4>
                      <Input
                        placeholder="Search views..."
                        value={filters.views}
                        onChange={(e) => handleFilterChange('views', e.target.value)}
                        className="h-8 text-sm"
                        type="number"
                      />
                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('views')}
                          className="text-xs"
                          disabled={!filters.views}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            {/* Responses Column */}
            <TableHead className="font-medium text-right">
              <div className="flex items-center justify-end">
                <span>Responses</span>
                <Popover open={openPopover === 'responses'} onOpenChange={(open) => setOpenPopover(open ? 'responses' : null)}>
                  <PopoverTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-6 w-6 ml-1">
                      <Filter className={`h-3 w-3 ${filters.responses ? 'text-primary' : 'text-gray-400'}`} />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-60 p-3" align="end">
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Filter Responses</h4>
                      <Input
                        placeholder="Search responses..."
                        value={filters.responses}
                        onChange={(e) => handleFilterChange('responses', e.target.value)}
                        className="h-8 text-sm"
                        type="number"
                      />
                      <div className="flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => clearFilter('responses')}
                          className="text-xs"
                          disabled={!filters.responses}
                        >
                          Clear
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => setOpenPopover(null)}
                          className="text-xs"
                        >
                          Apply
                        </Button>
                      </div>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </TableHead>

            <TableHead className="w-[80px]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredBroadcasts.length > 0 ? (
            filteredBroadcasts.map((broadcast) => (
              <TableRow key={broadcast.id} className="hover:bg-muted/30">
                <TableCell>
                  <div className="font-medium">{broadcast.title}</div>
                  <div className="text-xs text-muted-foreground">ID: {broadcast.id}</div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={`${broadcast.type === "two-way" ? "bg-blue-50 text-blue-600 border-blue-200" : "bg-gray-50 text-gray-600 border-gray-200"}`}>
                    <div className="flex items-center space-x-1">
                      {broadcast.type === "one-way" ? (
                        <MessageSquare className="h-3 w-3 mr-1" />
                      ) : (
                        <MessageSquareDiff className="h-3 w-3 mr-1" />
                      )}
                      <span className="capitalize">{broadcast.type}</span>
                    </div>
                  </Badge>
                </TableCell>
                <TableCell>{renderTarget(broadcast.targetType, broadcast.targetName)}</TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    <div className="flex items-center text-sm">
                      <Calendar className="h-3 w-3 mr-1 text-muted-foreground" />
                      {format(broadcast.sentDate, "MMM d, yyyy")}
                    </div>
                    <div className="flex items-center text-xs text-muted-foreground mt-1">
                      <Clock className="h-3 w-3 mr-1" />
                      {format(broadcast.sentDate, "h:mm a")}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={broadcast.status === "delivered" ? "success" : broadcast.status === "pending" ? "outline" : "destructive"} className="capitalize">
                    {broadcast.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end">
                    <Eye className="h-3 w-3 mr-1 text-muted-foreground" />
                    {broadcast.views}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  {broadcast.type === "two-way" ? (
                    <div className="font-medium">{broadcast.responses}</div>
                  ) : (
                    <span className="text-muted-foreground">—</span>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Download className="h-4 w-4 mr-2" />
                        Export Data
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={8} className="h-24 text-center">
                <div className="flex flex-col items-center justify-center text-muted-foreground">
                  <MessageSquare className="h-8 w-8 mb-2 opacity-40" />
                  <p className="text-sm font-medium">No broadcasts found</p>
                  <p className="text-xs mt-1">Try adjusting your filters or create a new broadcast</p>
                </div>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}
