import axios from 'axios';
import { LoginConfig } from '@/store/slices/authSlice';

const API_URL = 'https://client-api.acuizen.com';

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  id_token?: string;
  token_type: string;
  expires_in: number;
}

/**
 * Fetch login configuration from the API
 */
export const fetchLoginConfig = async (): Promise<LoginConfig> => {
  try {
    const response = await axios.get<LoginConfig>(`${API_URL}/auth/config`, {
      headers: {
        'Content-Type': 'application/json',
        'x-enterprise-id': 'koach',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Error fetching login config:', error);
    // Return mock config for development
    return {
      LOGO: "1748582084203acuizen.png",
      LOGIN_BUTTON_TEXT: "Internal Login",
      COGNITO_REGION: "ap-southeast-1",
      COGNITO_USER_DOMAIN: "https://internal-az.auth.ap-southeast-1.amazoncognito.com",
      COGNITO_USER_POOL_ID: "ap-southeast-1_tgSXcfydj",
      COGNITO_USER_APP_CLIENT_ID: "24724rrqraa868kbnk2if99h57",
      SELECTED_INDUSTRIES: ["Default Hazards", "Diving & ROV"]
    };
  }
};

/**
 * Exchange authorization code for tokens
 */
export const exchangeCodeForToken = async (
  code: string,
  redirectUri: string,
  cognitoDomain: string,
  clientId: string
): Promise<TokenResponse> => {
  try {
    const tokenEndpoint = `${cognitoDomain}/oauth2/token`;

    console.log('Token exchange details:', {
      cognitoDomain,
      tokenEndpoint,
      clientId,
      redirectUri
    });

    const body = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: clientId,
      code: code,
      redirect_uri: redirectUri,
    });

    const response = await axios.post<TokenResponse>(tokenEndpoint, body.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Error exchanging code for token:', error);
    throw error;
  }
};

/**
 * Refresh access token using refresh token
 */
export const refreshAccessToken = async (
  refreshToken: string,
  cognitoDomain: string,
  clientId: string
): Promise<TokenResponse> => {
  try {
    const tokenEndpoint = `${cognitoDomain}/oauth2/token`;

    const body = new URLSearchParams({
      grant_type: 'refresh_token',
      client_id: clientId,
      refresh_token: refreshToken,
    });

    const response = await axios.post<TokenResponse>(tokenEndpoint, body.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Error refreshing token:', error);
    throw error;
  }
};

/**
 * Revoke tokens (logout)
 */
export const revokeToken = async (
  token: string,
  cognitoDomain: string,
  clientId: string
): Promise<void> => {
  try {
    const revokeEndpoint = `${cognitoDomain}/oauth2/revoke`;

    const body = new URLSearchParams({
      token: token,
      client_id: clientId,
    });

    await axios.post(revokeEndpoint, body.toString(), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
  } catch (error) {
    console.error('Error revoking token:', error);
    // Don't throw error for logout, just log it
  }
};
