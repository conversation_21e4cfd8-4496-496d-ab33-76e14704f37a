import React from "react";
import { Button } from "@/components/ui/button";
import { Save, Eye, Download } from "lucide-react";
import { DroppedItem } from "@/types";

interface WorkspaceHeaderProps {
  items: DroppedItem[];
  onSave: () => void;
  onPreview: () => void;
  onExport?: () => void;
}

const WorkspaceHeader: React.FC<WorkspaceHeaderProps> = ({
  items,
  onSave,
  onPreview,
  onExport,
}) => {
  return (
    <div className="border-b border-slate-200 dark:border-slate-700 p-4 bg-white dark:bg-slate-800 shadow-sm flex items-center justify-between">
      <div>
        <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-200">Content Editor</h2>
        <p className="text-sm text-muted-foreground">
          {items.length} {items.length === 1 ? "component" : "components"} in workspace
        </p>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onPreview}
          className="flex items-center gap-1 h-9 px-3 border-slate-200 dark:border-slate-700 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
        >
          <Eye className="h-4 w-4" />
          Preview
        </Button>
        {onExport && (
          <Button
            variant="outline"
            size="sm"
            onClick={onExport}
            className="flex items-center gap-1 h-9 px-3 border-slate-200 dark:border-slate-700 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        )}
        <Button
          variant="default"
          size="sm"
          onClick={onSave}
          className="flex items-center gap-1 h-9 px-4 shadow-sm hover:shadow transition-all"
        >
          <Save className="h-4 w-4" />
          Save
        </Button>
      </div>
    </div>
  );
};

export default WorkspaceHeader;
