import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion, AnimatePresence } from 'framer-motion';
import { X, Filter, Database, BookOpen, Layers } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { knowledgeAreas, knowledgeTopics, knowledgeUnits } from '@/data/insightsData';

interface EnhancedKnowledgeSelectionFiltersProps {
  onSelectionChange: (selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  }) => void;
}

const EnhancedKnowledgeSelectionFilters: React.FC<EnhancedKnowledgeSelectionFiltersProps> = ({
  onSelectionChange,
}) => {
  const [selectedArea, setSelectedArea] = useState<string | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);

  const handleAreaChange = (value: string) => {
    setSelectedArea(value);
    setSelectedTopic(null);
    setSelectedUnits([]);
    onSelectionChange({ area: value, topic: null, units: [] });
  };

  const handleTopicChange = (value: string) => {
    setSelectedTopic(value);
    setSelectedUnits([]);
    onSelectionChange({ area: selectedArea, topic: value, units: [] });
  };

  const handleUnitChange = (value: string) => {
    // Check if unit is already selected
    if (selectedUnits.includes(value)) {
      return;
    }
    
    const newUnits = [...selectedUnits, value];
    setSelectedUnits(newUnits);
    onSelectionChange({ area: selectedArea, topic: selectedTopic, units: newUnits });
  };
  
  const removeUnit = (unitId: string) => {
    const newUnits = selectedUnits.filter(id => id !== unitId);
    setSelectedUnits(newUnits);
    onSelectionChange({ area: selectedArea, topic: selectedTopic, units: newUnits });
  };
  
  const resetFilters = () => {
    setSelectedArea(null);
    setSelectedTopic(null);
    setSelectedUnits([]);
    onSelectionChange({ area: null, topic: null, units: [] });
  };
  
  // Get the names for display
  const getAreaName = () => {
    return selectedArea ? knowledgeAreas.find(area => area.id === selectedArea)?.name : null;
  };
  
  const getTopicName = () => {
    if (!selectedArea || !selectedTopic) return null;
    return knowledgeTopics[selectedArea as keyof typeof knowledgeTopics]?.find(
      topic => topic.id === selectedTopic
    )?.name;
  };
  
  const getUnitName = (unitId: string) => {
    if (!selectedTopic) return null;
    return knowledgeUnits[selectedTopic as keyof typeof knowledgeUnits]?.find(
      unit => unit.id === unitId
    )?.name;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <CardTitle className="text-xl flex items-center gap-2">
              <Filter className="h-5 w-5 text-muted-foreground" />
              Knowledge Selection Filters
            </CardTitle>
            {(selectedArea || selectedTopic || selectedUnits.length > 0) && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={resetFilters}
                className="text-muted-foreground hover:text-foreground"
              >
                Reset Filters
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4 text-blue-500" />
                  <label className="text-sm font-medium">Knowledge Area</label>
                </div>
                <Select onValueChange={handleAreaChange} value={selectedArea || undefined}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Knowledge Area" />
                  </SelectTrigger>
                  <SelectContent>
                    {knowledgeAreas.map((area) => (
                      <SelectItem key={area.id} value={area.id}>
                        {area.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </motion.div>

            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <BookOpen className="h-4 w-4 text-purple-500" />
                  <label className="text-sm font-medium">Knowledge Topic</label>
                </div>
                <Select 
                  onValueChange={handleTopicChange} 
                  value={selectedTopic || undefined}
                  disabled={!selectedArea}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Knowledge Topic" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedArea &&
                      knowledgeTopics[selectedArea as keyof typeof knowledgeTopics]?.map((topic) => (
                        <SelectItem key={topic.id} value={topic.id}>
                          {topic.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </motion.div>

            <motion.div 
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
            >
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Layers className="h-4 w-4 text-green-500" />
                  <label className="text-sm font-medium">Knowledge Units</label>
                </div>
                <Select 
                  disabled={!selectedTopic}
                  value={selectedUnits.length > 0 ? selectedUnits[0] : undefined}
                  onValueChange={handleUnitChange}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue 
                      placeholder="Select Knowledge Units" 
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedTopic &&
                      knowledgeUnits[selectedTopic as keyof typeof knowledgeUnits]?.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>
                          {unit.name}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>
            </motion.div>
          </div>
          
          {/* Active Filters Display */}
          <AnimatePresence>
            {(selectedArea || selectedTopic || selectedUnits.length > 0) && (
              <motion.div 
                className="mt-6 p-4 border rounded-md bg-muted/30"
                initial={{ opacity: 0, height: 0, marginTop: 0 }}
                animate={{ opacity: 1, height: 'auto', marginTop: 24 }}
                exit={{ opacity: 0, height: 0, marginTop: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h3 className="text-sm font-medium mb-3">Active Filters:</h3>
                <div className="flex flex-wrap gap-2">
                  {selectedArea && (
                    <motion.div 
                      className="bg-blue-100 text-blue-800 text-xs rounded-full px-3 py-1 flex items-center gap-1"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.2 }}
                    >
                      <span>Area: {getAreaName()}</span>
                      <button 
                        onClick={() => resetFilters()}
                        className="ml-1 hover:text-blue-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </motion.div>
                  )}
                  
                  {selectedTopic && (
                    <motion.div 
                      className="bg-purple-100 text-purple-800 text-xs rounded-full px-3 py-1 flex items-center gap-1"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.2, delay: 0.1 }}
                    >
                      <span>Topic: {getTopicName()}</span>
                      <button 
                        onClick={() => {
                          setSelectedTopic(null);
                          setSelectedUnits([]);
                          onSelectionChange({ area: selectedArea, topic: null, units: [] });
                        }}
                        className="ml-1 hover:text-purple-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </motion.div>
                  )}
                  
                  {selectedUnits.map((unitId, index) => (
                    <motion.div 
                      key={unitId}
                      className="bg-green-100 text-green-800 text-xs rounded-full px-3 py-1 flex items-center gap-1"
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.2, delay: 0.1 + (index * 0.05) }}
                    >
                      <span>Unit: {getUnitName(unitId)}</span>
                      <button 
                        onClick={() => removeUnit(unitId)}
                        className="ml-1 hover:text-green-900"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default EnhancedKnowledgeSelectionFilters;
