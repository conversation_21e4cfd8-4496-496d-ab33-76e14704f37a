import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Eye, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import DataTable from "@/components/ui/data-table";
import { Progress } from "@/components/ui/progress";

export default function Certificate() {
  const [filter, setFilter] = useState({ location: "", department: "", division: "" });
  
  // Sample data matching the screenshot
  const data = [
    { 
      name: "<PERSON>", 
      employeeNo: "EMP001", 
      location: "New York", 
      department: "Engineering", 
      division: "Software Development", 
      allocatedKnowledge: [
        "React Fundamentals",
        "JavaScript Basics",
        "TypeScript Advanced"
      ],
      completedStatus: {
        "React Fundamentals": ["React Components", "React Hooks"],
        "JavaScript Basics": ["Variables & Functions"]
      },
      percentage: 65
    },
    { 
      name: "<PERSON>", 
      employeeNo: "EMP002", 
      location: "San Francisco", 
      department: "Design", 
      division: "UX Research", 
      allocatedKnowledge: [
        "UX Principles",
        "UI Design",
        "User Research"
      ],
      completedStatus: {
        "UX Principles": ["Usability Testing"],
        "User Research": ["Research Methods"]
      },
      percentage: 40
    },
    { 
      name: "Alex Johnson", 
      employeeNo: "EMP003", 
      location: "London", 
      department: "Marketing", 
      division: "Content Strategy", 
      allocatedKnowledge: [
        "Content Marketing",
        "SEO Basics",
        "Social Media"
      ],
      completedStatus: {
        "SEO Basics": ["SEO Fundamentals"],
        "Content Marketing": ["Content Planning", "Content Distribution"]
      },
      percentage: 85
    },
    { 
      name: "Sarah Williams", 
      employeeNo: "EMP004", 
      location: "Tokyo", 
      department: "Engineering", 
      division: "Quality Assurance", 
      allocatedKnowledge: [
        "Test Automation",
        "Manual Testing",
        "Performance Testing"
      ],
      completedStatus: {
        "Test Automation": ["Selenium Basics"],
        "Performance Testing": ["Load Testing"]
      },
      percentage: 50
    }
  ];

  const columns = [
    { Header: "Name", accessor: "name" },
    { Header: "Employee No.", accessor: "employeeNo" },
    { Header: "Location", accessor: "location" },
    { Header: "Department", accessor: "department" },
    { Header: "Division", accessor: "division" },
    { 
      Header: "Allocated Knowledge", 
      accessor: "allocatedKnowledge",
      Cell: ({ row }) => (
        <ul className="list-disc pl-5">
          {row.allocatedKnowledge.map((item, index) => (
            <li key={index}>{item}</li>
          ))}
        </ul>
      ) 
    },
    { 
      Header: "Completed Status", 
      accessor: "completedStatus",
      Cell: ({ row }) => (
        <div>
          {Object.keys(row.completedStatus).map((category, idx) => (
            <div key={idx} className="mb-2">
              <div className="font-medium">{category}</div>
              <ul className="list-disc pl-5">
                {row.completedStatus[category].map((item, index) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      ) 
    },
    { 
      Header: "Percentage", 
      accessor: "percentage",
      Cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Progress value={row.percentage} className="w-24" />
          <span>{row.percentage}%</span>
        </div>
      ) 
    },
    { 
      Header: "Actions", 
      accessor: "actions", 
      Cell: ({ row }) => (
        <div className="flex gap-2">
          <Button variant="ghost" size="sm" className="flex items-center gap-1">
            <Eye className="h-4 w-4" />
            <span>View</span>
          </Button>
          <Button variant="ghost" size="sm" className="flex items-center gap-1">
            <Download className="h-4 w-4" />
            <span>Download</span>
          </Button>
        </div>
      ) 
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Certificate Management</h1>
      </div>

      <Card className="p-6">
        <DataTable
          columns={columns}
          data={data}
          filter={filter}
          onFilterChange={setFilter}
          sortableColumns={["name", "employeeNo"]}
          filterableColumns={["location", "department", "division"]}
          searchable
          exportable
        />
      </Card>
    </div>
  );
}
