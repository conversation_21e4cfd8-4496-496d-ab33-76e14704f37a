import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>hart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend,
  ResponsiveContainer,
  Tooltip
} from "recharts";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from 'framer-motion';

interface AnimatedRadarChartProps {
  title: string;
  data: any[];
  dataKey: string;
  color?: string;
  height?: number;
  showLegend?: boolean;
  legendName?: string;
}

const AnimatedRadarChart: React.FC<AnimatedRadarChartProps> = ({
  title,
  data,
  dataKey,
  color = "#0EA5E9",
  height = 400,
  showLegend = true,
  legendName
}) => {
  const [chartData, setChartData] = useState<any[]>([]);
  
  // Animate the data on mount
  useEffect(() => {
    // Start with empty values
    const initialData = data.map(item => ({
      ...item,
      [dataKey]: 0
    }));
    
    setChartData(initialData);
    
    // Animate to actual values
    const timer = setTimeout(() => {
      setChartData(data);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [data, dataKey]);
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="overflow-hidden">
        <CardHeader className="pb-2">
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <CardTitle>{title}</CardTitle>
          </motion.div>
        </CardHeader>
        <CardContent className={`h-[${height}px]`}>
          <ResponsiveContainer width="100%" height={height}>
            <RadarChart 
              outerRadius={height / 3} 
              data={chartData}
              margin={{
                top: 20, right: 30, left: 20, bottom: 20,
              }}
            >
              <PolarGrid stroke="#E5E7EB" />
              <PolarAngleAxis dataKey="subject" />
              <PolarRadiusAxis angle={30} domain={[0, 100]} />
              <Radar 
                name={legendName || dataKey} 
                dataKey={dataKey} 
                stroke={color} 
                fill={color} 
                fillOpacity={0.5} 
                animationDuration={1500}
                animationEasing="ease-in-out"
              />
              {showLegend && <Legend />}
              <Tooltip 
                contentStyle={{ 
                  borderRadius: '8px', 
                  border: 'none', 
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  padding: '12px'
                }}
              />
            </RadarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AnimatedRadarChart;
