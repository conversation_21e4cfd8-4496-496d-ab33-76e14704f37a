
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface ProgressOverviewProps {
  completionRate: number;
}

const ProgressOverview: React.FC<ProgressOverviewProps> = ({ completionRate }) => {
  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Overall Progress</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Completion Rate</span>
            <span className="text-sm font-medium">{completionRate}%</span>
          </div>
          <Progress value={completionRate} className="h-2" />
          <div className="grid grid-cols-3 gap-4 text-center pt-4">
            <div className="bg-primary/10 rounded-md p-3">
              <div className="text-3xl font-bold">75%</div>
              <div className="text-xs text-muted-foreground">Knowledge Units</div>
            </div>
            <div className="bg-primary/10 rounded-md p-3">
              <div className="text-3xl font-bold">82%</div>
              <div className="text-xs text-muted-foreground">Assessments</div>
            </div>
            <div className="bg-primary/10 rounded-md p-3">
              <div className="text-3xl font-bold">68%</div>
              <div className="text-xs text-muted-foreground">Engagement</div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProgressOverview;
