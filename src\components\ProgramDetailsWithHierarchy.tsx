import React from "react";
import { ChevronRight } from "lucide-react";

interface ProgramDetailsWithHierarchyProps {
  programParts: { area: string; topic: string; unit: string }[];
  assignmentType?: "Training" | "CFD";
}

const ProgramDetailsWithHierarchy: React.FC<ProgramDetailsWithHierarchyProps> = ({
  programParts,
  assignmentType = "Training",
}) => {
  if (programParts.length === 0) {
    return (
      <div className="text-sm text-muted-foreground italic">
        {assignmentType === "Training" ? "No topics selected" : "No documents selected"}
      </div>
    );
  }

  // Group units by area and topic
  const groupedUnits: Record<string, Record<string, string[]>> = {};

  programParts.forEach(({ area, topic, unit }) => {
    if (!groupedUnits[area]) {
      groupedUnits[area] = {};
    }

    if (!groupedUnits[area][topic]) {
      groupedUnits[area][topic] = [];
    }

    groupedUnits[area][topic].push(unit);
  });

  return (
    <div className="space-y-6">
      {Object.entries(groupedUnits).map(([area, topicUnits]) => (
        <div key={area} className="space-y-4">
          {Object.entries(topicUnits).map(([topic, unitList]) => (
            <div key={`${area}-${topic}`} className="space-y-2">
              <div className="flex items-center text-sm font-medium">
                <span>{area}</span>
                <ChevronRight className="h-4 w-4 mx-1 text-muted-foreground" />
                <span>{topic}</span>
              </div>
              <div className="flex flex-col gap-2 mt-2">
                {assignmentType === "Training" ? (
                  // For Training, show just the topic with its area
                  <div className="p-3 bg-pink-50 rounded-md border border-pink-100">
                    <div className="text-sm font-medium text-pink-800 mb-1">
                      {area}
                    </div>
                    <div className="text-sm">
                      {topic}
                    </div>
                  </div>
                ) : (
                  // For CFD, show each unit with its area and topic
                  unitList.map((unit) => (
                    <div key={unit} className="p-3 bg-pink-50 rounded-md border border-pink-100">
                      <div className="text-sm font-medium text-pink-800 mb-1">
                        {area} – {topic}
                      </div>
                      <div className="text-sm">
                        {unit}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default ProgramDetailsWithHierarchy;
