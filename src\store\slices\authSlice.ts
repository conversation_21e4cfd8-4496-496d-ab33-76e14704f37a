import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface LoginConfig {
  LOGO: string;
  LOGIN_BUTTON_TEXT: string;
  COGNITO_REGION: string;
  COGNITO_USER_DOMAIN: string;
  COGNITO_USER_POOL_ID: string;
  COGNITO_USER_APP_CLIENT_ID: string;
  SELECTED_INDUSTRIES: string[];
}

export interface UserRole {
  id: string;
  name: string;
  maskName: string;
  maskId: string;
}

export interface User {
  id: string;
  email: string;
  firstName: string;
  roles: UserRole[];
  type: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  loginConfig: LoginConfig | null;
  tokens: AuthTokens | null;
  user: User | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: false,
  error: null,
  loginConfig: null,
  tokens: null,
  user: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setLoginConfig: (state, action: PayloadAction<LoginConfig>) => {
      state.loginConfig = action.payload;
    },
    setTokens: (state, action: PayloadAction<AuthTokens>) => {
      state.tokens = action.payload;
      state.isAuthenticated = true;
    },
    setLogin: (state) => {
      state.isAuthenticated = true;
      state.error = null;
    },
    setLogout: (state) => {
      state.isAuthenticated = false;
      state.tokens = null;
      state.user = null;
      state.error = null;
      // Clear localStorage
      localStorage.removeItem('COGNITO_USER_DOMAIN');
      localStorage.removeItem('COGNITO_USER_APP_CLIENT_ID');
    },
    validateTokens: (state) => {
      // Always ensure loading is false when validating
      state.isLoading = false;

      // Check if tokens exist and are not expired
      if (state.tokens?.accessToken) {
        try {
          const payload = JSON.parse(atob(state.tokens.accessToken.split('.')[1]));
          const currentTime = Date.now() / 1000;

          if (payload.exp < currentTime) {
            // Token is expired, logout
            state.isAuthenticated = false;
            state.tokens = null;
            state.user = null;
            state.error = 'Session expired';
          } else {
            // Token is valid, ensure user is marked as authenticated
            state.isAuthenticated = true;
            state.error = null;
          }
        } catch (error) {
          // Invalid token format, logout
          state.isAuthenticated = false;
          state.tokens = null;
          state.user = null;
          state.error = 'Invalid session';
        }
      } else if (state.isAuthenticated) {
        // No tokens but marked as authenticated, logout
        state.isAuthenticated = false;
        state.tokens = null;
        state.user = null;
      }
    },
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    forceStopLoading: (state) => {
      state.isLoading = false;
    },
  },
});

export const {
  setLoading,
  setError,
  setLoginConfig,
  setTokens,
  setLogin,
  setLogout,
  setUser,
  clearError,
  validateTokens,
  forceStopLoading,
} = authSlice.actions;

export default authSlice.reducer;
