import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";

interface KnowledgePullTrendsProps {
  selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  };
}

// Mock data for knowledge pull trends
const getKnowledgePullTrends = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return {
    area: [
      { month: 'Jan', value: 65 },
      { month: 'Feb', value: 68 },
      { month: 'Mar', value: 72 },
      { month: 'Apr', value: 75 },
      { month: 'May', value: 78 },
      { month: 'Jun', value: 82 },
    ],
    group: [
      { month: 'Jan', value: 60 },
      { month: 'Feb', value: 63 },
      { month: 'Mar', value: 67 },
      { month: 'Apr', value: 70 },
      { month: 'May', value: 73 },
      { month: 'Jun', value: 77 },
    ],
    enterprise: [
      { month: 'Jan', value: 55 },
      { month: 'Feb', value: 58 },
      { month: 'Mar', value: 62 },
      { month: 'Apr', value: 65 },
      { month: 'May', value: 68 },
      { month: 'Jun', value: 72 },
    ],
  };
};

const KnowledgePullTrends: React.FC<KnowledgePullTrendsProps> = ({ selection }) => {
  const [activeTab, setActiveTab] = useState('area');
  const trendsData = getKnowledgePullTrends(selection);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Knowledge Pull Trends</CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="area" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="area">Area</TabsTrigger>
            <TabsTrigger value="group">Group</TabsTrigger>
            <TabsTrigger value="enterprise">Enterprise</TabsTrigger>
          </TabsList>
          <TabsContent value="area" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={trendsData.area}
                margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, 'Pull Rate']} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="value"
                  name="Area Pull Rate"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="group" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={trendsData.group}
                margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, 'Pull Rate']} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="value"
                  name="Group Pull Rate"
                  stroke="#10B981"
                  strokeWidth={2}
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
          <TabsContent value="enterprise" className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={trendsData.enterprise}
                margin={{ top: 20, right: 30, left: 20, bottom: 10 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}%`, 'Pull Rate']} />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="value"
                  name="Enterprise Pull Rate"
                  stroke="#8B5CF6"
                  strokeWidth={2}
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default KnowledgePullTrends;