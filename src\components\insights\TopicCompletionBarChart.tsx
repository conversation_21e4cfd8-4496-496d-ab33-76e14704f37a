
import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { unitCompletionByTopic } from '@/data/analyticsData';

const TopicCompletionBarChart: React.FC = () => {
  const data = unitCompletionByTopic.map(item => ({
    topic: item.topic,
    completed: item.completed,
    remaining: item.total - item.completed
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Unit Completion by Topic</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{ top: 20, right: 30, left: 20, bottom: 30 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="topic" angle={-45} textAnchor="end" height={70} />
            <YAxis />
            <Tooltip />
            <Legend />
            <Bar dataKey="completed" stackId="a" fill="#8884d8" name="Completed" />
            <Bar dataKey="remaining" stackId="a" fill="#82ca9d" name="Remaining" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default TopicCompletionBarChart;
