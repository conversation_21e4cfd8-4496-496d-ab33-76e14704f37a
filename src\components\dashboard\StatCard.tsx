
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { useState } from "react";

interface StatCardProps {
  title: string;
  count: number | string;
  icon: LucideIcon;
  tooltipText?: string;
  className?: string;
  iconColor?: string;
  trend?: number;
  trendLabel?: string;
}

export default function StatCard({
  title,
  count,
  icon: Icon,
  tooltipText,
  className,
  iconColor = "text-primary",
  trend,
  trendLabel
}: StatCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  // Format the count if it's a number
  const formattedCount = typeof count === 'number' ?
    count.toLocaleString() : count;

  // Determine trend icon and color
  const isTrendUp = trend && trend > 0;
  const isTrendDown = trend && trend < 0;
  const trendColor = isTrendUp ? 'text-green-500' : isTrendDown ? 'text-red-500' : 'text-gray-500';

  return (
    <motion.div
      whileHover={{ y: -5, transition: { duration: 0.2 } }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
    >
      <Card className={cn("overflow-hidden transition-all duration-300", className, isHovered ? "shadow-lg" : "")}>
        <CardContent className="p-6 relative">
          <motion.div
            className="flex flex-col"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm font-medium opacity-90">{title}</p>
                <p className="text-xs opacity-75 mt-0.5">{tooltipText}</p>
              </div>
              <motion.div
                className={cn("p-2 rounded-full", iconColor)}
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.95 }}
              >
                <Icon size={20} />
              </motion.div>
            </div>

            <motion.h3
              className="text-4xl font-bold mt-4"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              {formattedCount}
            </motion.h3>

            {trend !== undefined && (
              <motion.div
                className={`flex items-center mt-2 ${trendColor}`}
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.3 }}
              >
                {isTrendUp ? (
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                  </svg>
                ) : isTrendDown ? (
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                ) : (
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h14" />
                  </svg>
                )}
                <span className="text-sm font-medium">
                  {Math.abs(trend)}% {trendLabel || (isTrendUp ? 'increase' : isTrendDown ? 'decrease' : 'no change')}
                </span>
              </motion.div>
            )}
          </motion.div>

          {/* Background decoration */}
          <motion.div
            className="absolute -bottom-6 -right-6 w-24 h-24 rounded-full opacity-10"
            style={{ background: isHovered ? 'currentColor' : 'transparent' }}
            animate={{ scale: isHovered ? 1.2 : 1 }}
            transition={{ duration: 0.3 }}
          />
        </CardContent>
      </Card>
    </motion.div>
  );
}
