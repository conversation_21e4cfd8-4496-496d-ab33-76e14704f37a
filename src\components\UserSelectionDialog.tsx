import React, { useState, useMemo } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Search, Users, Check, X } from "lucide-react";
import { toast } from "@/hooks/use-toast";

// User interface matching the structure from Users.tsx
interface User {
  id: string;
  name: string;
  employeeNo: string;
  email: string;
  phone: string;
  dateOfJoining: string;
  division: string;
  department: string;
  location: string;
  pullRate: number;
  knowledgeIndex: number;
  status: string;
  groups?: string[];
}

interface UserSelectionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAssign: (selectedUsers: User[]) => void;
  title?: string;
  description?: string;
  assignmentItem?: {
    area: string;
    topic: string;
    topicId: string;
  };
}

// Mock user data (same as in Users.tsx)
const mockUsers: User[] = [
  {
    id: "1",
    name: "Alex Johnson",
    employeeNo: "EMP-001",
    email: "<EMAIL>",
    phone: "******-567-8901",
    dateOfJoining: "2021-05-12",
    division: "Clinical Support",
    department: "Biomedical",
    location: "KHS",
    pullRate: 85,
    knowledgeIndex: 92,
    status: "active",
    groups: ["KCN-Operations-Facility Services", "KCN-Information Technology-Information Technology"],
  },
  {
    id: "2",
    name: "Jamie Smith",
    employeeNo: "EMP-002",
    email: "<EMAIL>",
    phone: "******-567-8902",
    dateOfJoining: "2022-02-15",
    division: "Facility Services",
    department: "Engineering",
    location: "KCN",
    pullRate: 78,
    knowledgeIndex: 81,
    status: "active",
  },
  {
    id: "3",
    name: "Taylor Brown",
    employeeNo: "EMP-003",
    email: "<EMAIL>",
    phone: "******-567-8903",
    dateOfJoining: "2020-11-20",
    division: "Clinical Nursing",
    department: "Nursing",
    location: "KCN",
    pullRate: 92,
    knowledgeIndex: 88,
    status: "active",
  },
  {
    id: "4",
    name: "Morgan Wilson",
    employeeNo: "EMP-004",
    email: "<EMAIL>",
    phone: "******-567-8904",
    dateOfJoining: "2023-01-10",
    division: "Information Technology",
    department: "IT Services",
    location: "KHS",
    pullRate: 76,
    knowledgeIndex: 85,
    status: "active",
  },
  {
    id: "5",
    name: "Casey Davis",
    employeeNo: "EMP-005",
    email: "<EMAIL>",
    phone: "******-567-8905",
    dateOfJoining: "2022-07-08",
    division: "Operations",
    department: "OPD",
    location: "KTN",
    pullRate: 89,
    knowledgeIndex: 94,
    status: "blocked",
  },
  {
    id: "6",
    name: "Jordan Miller",
    employeeNo: "EMP-006",
    email: "<EMAIL>",
    phone: "******-567-8906",
    dateOfJoining: "2021-09-14",
    division: "Facility Services",
    department: "Facility Services",
    location: "KTN",
    pullRate: 91,
    knowledgeIndex: 87,
    status: "active",
  },
  {
    id: "7",
    name: "Robin Lee",
    employeeNo: "EXT-001",
    email: "<EMAIL>",
    phone: "******-567-7001",
    dateOfJoining: "2022-06-10",
    division: "Clinical Nursing",
    department: "Nursing",
    location: "KTN",
    pullRate: 75,
    knowledgeIndex: 80,
    status: "active",
    groups: ["KCN-Clinical Research-Clinical Research"],
  },
  {
    id: "8",
    name: "Sam Green",
    employeeNo: "EXT-002",
    email: "<EMAIL>",
    phone: "******-567-7002",
    dateOfJoining: "2021-10-05",
    division: "Clinical Support",
    department: "Critical Care",
    location: "KTN",
    pullRate: 68,
    knowledgeIndex: 77,
    status: "active",
  },
];

export function UserSelectionDialog({
  open,
  onOpenChange,
  onAssign,
  title = "Assign Users",
  description,
  assignmentItem,
}: UserSelectionDialogProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [isAssigning, setIsAssigning] = useState(false);

  // Filter active users and apply search
  const filteredUsers = useMemo(() => {
    return mockUsers
      .filter(user => user.status === "active")
      .filter(user => {
        if (!searchTerm) return true;
        const searchLower = searchTerm.toLowerCase();
        return (
          user.name.toLowerCase().includes(searchLower) ||
          user.employeeNo.toLowerCase().includes(searchLower) ||
          user.email.toLowerCase().includes(searchLower) ||
          user.department.toLowerCase().includes(searchLower) ||
          user.division.toLowerCase().includes(searchLower)
        );
      });
  }, [searchTerm]);

  const handleSelectAll = () => {
    setSelectedUserIds(filteredUsers.map(user => user.id));
  };

  const handleClearAll = () => {
    setSelectedUserIds([]);
  };

  const handleUserToggle = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUserIds(prev => [...prev, userId]);
    } else {
      setSelectedUserIds(prev => prev.filter(id => id !== userId));
    }
  };

  const handleAssign = async () => {
    if (selectedUserIds.length === 0) {
      toast({
        title: "No users selected",
        description: "Please select at least one user to assign.",
        variant: "destructive",
      });
      return;
    }

    setIsAssigning(true);
    
    try {
      const selectedUsers = mockUsers.filter(user => selectedUserIds.includes(user.id));
      await onAssign(selectedUsers);
      
      toast({
        title: "Assignment successful",
        description: `Successfully assigned ${selectedUsers.length} user(s) to ${assignmentItem?.topic || 'the item'}.`,
      });
      
      // Reset state and close dialog
      setSelectedUserIds([]);
      setSearchTerm("");
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Assignment failed",
        description: "There was an error assigning users. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAssigning(false);
    }
  };

  const selectedUsers = mockUsers.filter(user => selectedUserIds.includes(user.id));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users size={20} />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description || (assignmentItem 
              ? `Select users to assign to ${assignmentItem.area} → ${assignmentItem.topic} (${assignmentItem.topicId})`
              : "Select users from the list below"
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 flex flex-col gap-4 min-h-0">
          {/* Search and selection controls */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search users by name, employee ID, email, department..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                disabled={filteredUsers.length === 0}
              >
                <Check size={14} className="mr-1" />
                Select All ({filteredUsers.length})
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearAll}
                disabled={selectedUserIds.length === 0}
              >
                <X size={14} className="mr-1" />
                Clear All
              </Button>
            </div>
          </div>

          {/* Selected users summary */}
          {selectedUserIds.length > 0 && (
            <div className="bg-muted/50 rounded-lg p-3">
              <div className="text-sm font-medium mb-2">
                Selected Users ({selectedUserIds.length}):
              </div>
              <div className="flex flex-wrap gap-1">
                {selectedUsers.slice(0, 5).map(user => (
                  <Badge key={user.id} variant="secondary" className="text-xs">
                    {user.name}
                  </Badge>
                ))}
                {selectedUsers.length > 5 && (
                  <Badge variant="secondary" className="text-xs">
                    +{selectedUsers.length - 5} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Users table */}
          <div className="flex-1 border rounded-lg overflow-hidden">
            <ScrollArea className="h-full">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={filteredUsers.length > 0 && selectedUserIds.length === filteredUsers.length}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            handleSelectAll();
                          } else {
                            handleClearAll();
                          }
                        }}
                      />
                    </TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Employee ID</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Division</TableHead>
                    <TableHead>Location</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length > 0 ? (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedUserIds.includes(user.id)}
                            onCheckedChange={(checked) => handleUserToggle(user.id, !!checked)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{user.name}</div>
                            <div className="text-sm text-muted-foreground">
                              Knowledge Index: {user.knowledgeIndex}%
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="font-mono text-sm">{user.employeeNo}</TableCell>
                        <TableCell className="text-sm">{user.email}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{user.department}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{user.division}</Badge>
                        </TableCell>
                        <TableCell>{user.location}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-muted-foreground">
                          {searchTerm ? "No users found matching your search." : "No active users available."}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </ScrollArea>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isAssigning}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAssign}
            disabled={selectedUserIds.length === 0 || isAssigning}
            className="gap-2"
          >
            {isAssigning ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Assigning...
              </>
            ) : (
              <>
                <Users size={16} />
                Assign {selectedUserIds.length > 0 ? `(${selectedUserIds.length})` : ''}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
