import { ReactNode } from 'react';

export type ContentMode = 'communicate' | 'feedback';

export interface ComponentBase {
  id: string;
  type: string;
  position: number;
  required?: boolean;
  allowFromLibrary?: boolean;
}

// Communicate component types
export interface ImageComponent extends ComponentBase {
  type: 'image';
  src?: string;
  alt?: string;
  fileSize?: number;
}

export interface VideoComponent extends ComponentBase {
  type: 'video';
  src?: string;
  title?: string;
  fileSize?: number;
}

export interface YouTubeComponent extends ComponentBase {
  type: 'youtube';
  videoId?: string;
  url?: string;
}

export interface WebLinkComponent extends ComponentBase {
  type: 'weblink';
  url?: string;
  title?: string;
  thumbnail?: string;
}

export interface TextComponent extends ComponentBase {
  type: 'text';
  content?: string;
}

export interface AudioComponent extends ComponentBase {
  type: 'audio';
  src?: string;
  title?: string;
  fileSize?: number;
  isRecorded?: boolean;
}

export interface EmbedComponent extends ComponentBase {
  type: 'embed';
  code?: string;
}

export interface ScormComponent extends ComponentBase {
  type: 'scorm';
  src?: string;
  fileName?: string;
  fileSize?: number;
  title?: string;
}

export interface WebGLComponent extends ComponentBase {
  type: 'webgl';
  src?: string;
  fileName?: string;
  fileSize?: number;
  title?: string;
}

// Feedback component types
export interface MCQComponent extends ComponentBase {
  type: 'mcq';
  question?: string;
  options?: { id: string; text: string }[];
  allowMultiple?: boolean;
  required: boolean;
}

export interface TextboxComponent extends ComponentBase {
  type: 'textbox';
  label?: string;
  placeholder?: string;
  required: boolean;
}

export interface FeedbackImageComponent extends ComponentBase {
  type: 'feedback-image';
  label?: string;
  required: boolean;
  allowFromLibrary: boolean;
  src?: string;
  fileSize?: number;
}

export interface FeedbackVideoComponent extends ComponentBase {
  type: 'feedback-video';
  label?: string;
  required: boolean;
  allowFromLibrary: boolean;
  src?: string;
  fileSize?: number;
}

export interface FeedbackAudioComponent extends ComponentBase {
  type: 'feedback-audio';
  label?: string;
  required: boolean;
  allowFromLibrary: boolean;
  src?: string;
  fileSize?: number;
  isRecorded?: boolean;
}

export interface OptionComponent extends ComponentBase {
  type: 'option';
  label?: string;
  options?: { id: string; text: string }[];
  required: boolean;
}

export interface DropdownComponent extends ComponentBase {
  type: 'dropdown';
  label?: string;
  options?: { id: string; text: string }[];
  required: boolean;
}

export interface SignComponent extends ComponentBase {
  type: 'sign';
  label?: string;
  required: boolean;
}

export interface CheckboxComponent extends ComponentBase {
  type: 'checkbox';
  label?: string;
  options?: { id: string; text: string }[];
  required: boolean;
}

export interface StarComponent extends ComponentBase {
  type: 'star';
  label?: string;
  maxStars?: number;
  required: boolean;
}

export interface DateComponent extends ComponentBase {
  type: 'date';
  label?: string;
  required: boolean;
}

export interface TimeComponent extends ComponentBase {
  type: 'time';
  label?: string;
  hasRange?: boolean;
  required: boolean;
}

export interface DurationComponent extends ComponentBase {
  type: 'duration';
  label?: string;
  required: boolean;
}

export interface PhoneComponent extends ComponentBase {
  type: 'phone';
  label?: string;
  placeholder?: string;
  required: boolean;
}

export interface AlphaNumericComponent extends ComponentBase {
  type: 'alphanumeric';
  label?: string;
  placeholder?: string;
  pattern?: string;
  containNumber?: boolean;
  containLetters?: boolean;
  required: boolean;
}

export type CommunicateComponentType =
  | ImageComponent
  | VideoComponent
  | YouTubeComponent
  | WebLinkComponent
  | TextComponent
  | AudioComponent
  | EmbedComponent
  | ScormComponent
  | WebGLComponent;

export type FeedbackComponentType =
  | MCQComponent
  | TextboxComponent
  | FeedbackImageComponent
  | FeedbackVideoComponent
  | FeedbackAudioComponent
  | OptionComponent
  | DropdownComponent
  | SignComponent
  | CheckboxComponent
  | StarComponent
  | DateComponent
  | TimeComponent
  | DurationComponent
  | PhoneComponent
  | AlphaNumericComponent;

export type ContentComponent = CommunicateComponentType | FeedbackComponentType;

export interface DraggableItemProps {
  type: string;
  icon: ReactNode;
  label: string;
  description?: string;
  onDragStart: (type: string) => void;
}

export interface DroppedItem {
  id: string;
  type: string;
  data: ContentComponent;
}
