import React from "react";
import { ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface HorizontalOrganizationTreeProps {
  selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  };
}

export function HorizontalOrganizationTree({ selection }: HorizontalOrganizationTreeProps) {
  // Check if there are any selections
  const hasSelections = 
    selection.businessUnits.length > 0 ||
    selection.departmentGroups.length > 0 ||
    selection.departments.length > 0 ||
    selection.divisions.length > 0 ||
    selection.subDivisions.length > 0 ||
    selection.categories.length > 0 ||
    selection.grades.length > 0 ||
    selection.designations.length > 0;

  if (!hasSelections) {
    return <div className="text-sm text-muted-foreground italic">No selections made</div>;
  }

  // Create a flat list of all selections for the summary section
  const allSelections = [
    ...selection.businessUnits.map(item => ({ type: 'Business Units', value: item })),
    ...selection.departmentGroups.map(item => ({ type: 'Department Groups', value: item })),
    ...selection.departments.map(item => ({ type: 'Departments', value: item })),
    ...selection.divisions.map(item => ({ type: 'Divisions', value: item })),
    ...selection.subDivisions.map(item => ({ type: 'Sub-Divisions', value: item })),
    ...selection.categories.map(item => ({ type: 'Categories', value: item })),
    ...selection.grades.map(item => ({ type: 'Grades', value: item })),
    ...selection.designations.map(item => ({ type: 'Designations', value: item })),
  ];

  // Function to get badge color based on selection type
  const getBadgeClass = (type: string) => {
    switch (type) {
      case 'Business Units':
        return "bg-blue-50 text-blue-700 border-blue-200";
      case 'Department Groups':
        return "bg-purple-50 text-purple-700 border-purple-200";
      case 'Departments':
        return "bg-green-50 text-green-700 border-green-200";
      case 'Divisions':
        return "bg-orange-50 text-orange-700 border-orange-200";
      case 'Sub-Divisions':
        return "bg-red-50 text-red-700 border-red-200";
      case 'Categories':
        return "bg-teal-50 text-teal-700 border-teal-200";
      case 'Grades':
        return "bg-indigo-50 text-indigo-700 border-indigo-200";
      case 'Designations':
        return "bg-pink-50 text-pink-700 border-pink-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  // Create hierarchical tree rows
  const renderHierarchicalRows = () => {
    const rows = [];

    // For each business unit
    for (const bu of selection.businessUnits) {
      // For each department group
      for (const dg of selection.departmentGroups.length > 0 ? selection.departmentGroups : [""]) {
        // For each department
        for (const dept of selection.departments.length > 0 ? selection.departments : [""]) {
          // For each division
          for (const div of selection.divisions.length > 0 ? selection.divisions : [""]) {
            // For each sub-division
            for (const subdiv of selection.subDivisions.length > 0 ? selection.subDivisions : [""]) {
              // For each category
              for (const cat of selection.categories.length > 0 ? selection.categories : [""]) {
                // For each grade
                for (const grade of selection.grades.length > 0 ? selection.grades : [""]) {
                  // For each designation
                  for (const desig of selection.designations.length > 0 ? selection.designations : [""]) {
                    // Skip empty rows
                    if (!dg && !dept && !div && !subdiv && !cat && !grade && !desig) continue;

                    rows.push(
                      <div key={`${bu}-${dg}-${dept}-${div}-${subdiv}-${cat}-${grade}-${desig}`} className="flex items-center flex-wrap gap-1 mb-2 border-b pb-2">
                        <Badge variant="outline" className={getBadgeClass('Business Units')}>
                          {bu}
                        </Badge>
                        
                        {dg && (
                          <>
                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                            <Badge variant="outline" className={getBadgeClass('Department Groups')}>
                              {dg}
                            </Badge>
                          </>
                        )}
                        
                        {dept && (
                          <>
                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                            <Badge variant="outline" className={getBadgeClass('Departments')}>
                              {dept}
                            </Badge>
                          </>
                        )}
                        
                        {div && (
                          <>
                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                            <Badge variant="outline" className={getBadgeClass('Divisions')}>
                              {div}
                            </Badge>
                          </>
                        )}
                        
                        {subdiv && (
                          <>
                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                            <Badge variant="outline" className={getBadgeClass('Sub-Divisions')}>
                              {subdiv}
                            </Badge>
                          </>
                        )}
                        
                        {cat && (
                          <>
                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                            <Badge variant="outline" className={getBadgeClass('Categories')}>
                              {cat}
                            </Badge>
                          </>
                        )}
                        
                        {grade && (
                          <>
                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                            <Badge variant="outline" className={getBadgeClass('Grades')}>
                              {grade}
                            </Badge>
                          </>
                        )}
                        
                        {desig && (
                          <>
                            <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                            <Badge variant="outline" className={getBadgeClass('Designations')}>
                              {desig}
                            </Badge>
                          </>
                        )}
                      </div>
                    );
                  }
                }
              }
            }
          }
        }
      }
    }

    return rows;
  };

  return (
    <div className="space-y-4">
      <div className="text-sm font-medium mb-2">Selection Summary</div>
      
      {/* Horizontal tree structure */}
      <div className="border rounded-md p-3 bg-muted/5">
        {renderHierarchicalRows()}
      </div>
      
      {/* Flat list of all selections */}
      <div className="mt-4">
        <div className="text-xs text-muted-foreground mb-2">All Selected Items:</div>
        <div className="flex flex-wrap gap-2">
          {allSelections.map((item, index) => (
            <Badge key={index} variant="outline" className={getBadgeClass(item.type)}>
              {item.value}
            </Badge>
          ))}
        </div>
      </div>
    </div>
  );
}
