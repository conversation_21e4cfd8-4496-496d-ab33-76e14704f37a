import React, { createContext, useContext, useState, ReactNode } from 'react';

// Types for knowledge structure
export interface KnowledgeArea {
  id: string;
  name: string;
  type: "area";
  icon?: string;
  color?: string;
  children: KnowledgeTopic[];
  topicCount?: number;
  description?: string;
}

export interface KnowledgeTopic {
  id: string;
  name: string;
  type: "topic";
  unitCount?: number;
  children: KnowledgeUnit[];
  description?: string;
}

export interface KnowledgeUnit {
  id: string;
  name: string;
  type: "unit";
  stepCount?: number;
  createdOn?: string;
  updatedOn?: string;
  isDefault?: boolean;
  duration?: string;
  difficulty?: "Beginner" | "Intermediate" | "Advanced";
  completionRate?: number;
  description?: string;
  category?: string;
  itemCount?: number;
}

export interface ChecklistFormArea {
  id: string;
  name: string;
  type: "area";
  topicCount: number;
  children: ChecklistFormTopic[];
  description?: string;
}

export interface ChecklistFormTopic {
  id: string;
  name: string;
  type: "topic";
  unitCount: number;
  children: ChecklistFormUnit[];
  description?: string;
}

export interface ChecklistFormUnit {
  id: string;
  name: string;
  type: "unit";
  category: "checklist" | "form";
  itemCount: number;
  createdOn: string;
  updatedOn: string;
  description?: string;
}

export type KnowledgeItem = KnowledgeArea | KnowledgeTopic | KnowledgeUnit;
export type ChecklistFormItem = ChecklistFormArea | ChecklistFormTopic | ChecklistFormUnit;

// Initial data for knowledge areas, topics, and units
const initialKnowledgeData: KnowledgeArea[] = [];

// Initial checklist/form data
const initialChecklistFormData: ChecklistFormArea[] = [];

// Context interface
interface KnowledgeContextType {
  knowledgeData: KnowledgeArea[];
  checklistFormData: ChecklistFormArea[];
  setKnowledgeData: (data: KnowledgeArea[]) => void;
  setChecklistFormData: (data: ChecklistFormArea[]) => void;
  addKnowledgeItem: (type: "area" | "topic" | "unit", parentId?: string, item?: Partial<KnowledgeItem>) => void;
  updateKnowledgeItem: (item: KnowledgeItem) => void;
  deleteKnowledgeItem: (item: KnowledgeItem) => void;
}

// Create context
const KnowledgeContext = createContext<KnowledgeContextType | undefined>(undefined);

// Provider component
export const KnowledgeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [knowledgeData, setKnowledgeData] = useState<KnowledgeArea[]>(initialKnowledgeData);
  const [checklistFormData, setChecklistFormData] = useState<ChecklistFormArea[]>(initialChecklistFormData);

  const addKnowledgeItem = (type: "area" | "topic" | "unit", parentId?: string, item?: Partial<KnowledgeItem>) => {
    // Implementation for adding items
    // This would be implemented based on the existing logic in Curate.tsx
  };

  const updateKnowledgeItem = (item: KnowledgeItem) => {
    // Implementation for updating items
    // This would be implemented based on the existing logic in Curate.tsx
  };

  const deleteKnowledgeItem = (item: KnowledgeItem) => {
    // Implementation for deleting items
    // This would be implemented based on the existing logic in Curate.tsx
  };

  const value: KnowledgeContextType = {
    knowledgeData,
    checklistFormData,
    setKnowledgeData,
    setChecklistFormData,
    addKnowledgeItem,
    updateKnowledgeItem,
    deleteKnowledgeItem,
  };

  return (
    <KnowledgeContext.Provider value={value}>
      {children}
    </KnowledgeContext.Provider>
  );
};

// Hook to use the context
export const useKnowledge = () => {
  const context = useContext(KnowledgeContext);
  if (context === undefined) {
    throw new Error('useKnowledge must be used within a KnowledgeProvider');
  }
  return context;
};
