import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Filter, X } from "lucide-react";

// Complex interface for the training library (existing usage)
interface ComplexTableColumnFilterProps {
  columnName: string;
  fieldKey: string;
  uniqueValues: string[];
  filterValue: string;
  selectedValue: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onFilterChange: (field: string, value: string) => void;
  onDropdownSelect: (field: string, value: string) => void;
  onClearFilter: (field: string) => void;
  placeholder?: string;
}

// Simple interface for the new library tabs
interface SimpleTableColumnFilterProps {
  placeholder: string;
  value: string;
  onChange: (value: string) => void;
  options: string[];
}

// Union type for props
type TableColumnFilterProps = ComplexTableColumnFilterProps | SimpleTableColumnFilterProps;

// Type guard to check if props are for complex interface
function isComplexProps(props: TableColumnFilterProps): props is ComplexTableColumnFilterProps {
  return 'columnName' in props && 'fieldKey' in props;
}

export function TableColumnFilter(props: TableColumnFilterProps) {
  // Handle complex interface (existing training library usage)
  if (isComplexProps(props)) {
    const {
      columnName,
      fieldKey,
      uniqueValues,
      filterValue,
      selectedValue,
      isOpen,
      onOpenChange,
      onFilterChange,
      onDropdownSelect,
      onClearFilter,
      placeholder = "Type to search..."
    } = props;

    return (
      <div className="flex items-center justify-between">
        <span>{columnName}</span>
        <Popover open={isOpen} onOpenChange={onOpenChange}>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="h-6 w-6">
              <Filter className={`h-3 w-3 ${filterValue || selectedValue ? 'text-primary' : 'text-gray-400'}`} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-72 p-3" align="end">
            <div className="space-y-4">
              <h4 className="font-medium text-sm">Filter {columnName}</h4>

              {/* Dropdown filter */}
              <div className="space-y-1">
                <label className="text-xs font-medium">Select from dropdown</label>
                <Select
                  value={selectedValue}
                  onValueChange={(value) => onDropdownSelect(fieldKey, value)}
                >
                  <SelectTrigger className="h-8 text-sm">
                    <SelectValue placeholder={`Select ${columnName.toLowerCase()}`} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All {columnName.toLowerCase()}s</SelectItem>
                    {uniqueValues.map((value) => (
                      <SelectItem key={value} value={value}>{value}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <span className="w-full border-t"></span>
                </div>
                <div className="relative flex justify-center text-xs">
                  <span className="bg-background px-2 text-muted-foreground">OR</span>
                </div>
              </div>

              {/* Text search filter */}
              <div className="space-y-1">
                <label className="text-xs font-medium">Search by text</label>
                <Input
                  placeholder={placeholder}
                  value={filterValue}
                  onChange={(e) => onFilterChange(fieldKey, e.target.value)}
                  className="h-8 text-sm"
                />
              </div>

              <div className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onClearFilter(fieldKey)}
                  className="text-xs"
                  disabled={!filterValue && !selectedValue}
                >
                  Clear
                </Button>
                <Button
                  size="sm"
                  onClick={() => onOpenChange(false)}
                  className="text-xs"
                >
                  Apply
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    );
  }

  // Handle simple interface (new library tabs usage)
  const { placeholder, value, onChange, options } = props;
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <Input
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="pr-20"
      />
      <div className="absolute right-1 top-1/2 -translate-y-1/2 flex gap-1">
        {value && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => onChange("")}
          >
            <X size={12} />
          </Button>
        )}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button variant="ghost" size="icon" className="h-6 w-6">
              <Filter size={12} className={value ? 'text-primary' : 'text-muted-foreground'} />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-48 p-2" align="end">
            <Select value={value} onValueChange={(val) => { onChange(val); setIsOpen(false); }}>
              <SelectTrigger className="h-8">
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All</SelectItem>
                {options.map((option) => (
                  <SelectItem key={option} value={option}>{option}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
