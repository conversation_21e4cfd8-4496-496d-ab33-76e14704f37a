import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import { validateTokens, setLoading, forceStopLoading } from '@/store/slices/authSlice';

interface AuthInitializerProps {
  children: React.ReactNode;
}

const AuthInitializer: React.FC<AuthInitializerProps> = ({ children }) => {
  const dispatch = useDispatch();
  const { tokens } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    // Always validate tokens on app startup, regardless of authentication state
    dispatch(validateTokens());

    // Ensure loading state is cleared after a short delay
    const timer1 = setTimeout(() => {
      dispatch(setLoading(false));
    }, 100);

    // Force stop loading after 2 seconds as a safety mechanism
    const timer2 = setTimeout(() => {
      dispatch(forceStopLoading());
    }, 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, [dispatch]);

  return <>{children}</>;
};

export default AuthInitializer;
