import React from 'react';
import { ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface AssignedContentDropdownProps {
  title: string;
  count?: number;
  children: React.ReactNode;
  className?: string;
}

const AssignedContentDropdown = ({
  title,
  count,
  children,
  className,
}: AssignedContentDropdownProps) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className={cn("border-b", className)}>
      <div 
        className="flex items-center justify-between p-3 cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        <div className="font-medium">{title}</div>
        <div className="flex items-center gap-2">
          {count !== undefined && count > 0 && (
            <span className="text-sm text-muted-foreground">{count} selected</span>
          )}
          <ChevronDown 
            className={cn(
              "h-5 w-5 text-muted-foreground transition-transform", 
              isOpen && "transform rotate-180"
            )} 
          />
        </div>
      </div>
      {isOpen && (
        <div className="p-3 pt-0">
          {children}
        </div>
      )}
    </div>
  );
};

export default AssignedContentDropdown;
