import apiService from '@/services/apiService';

/**
 * Test function to verify knowledge management API calls
 * This can be called from the browser console for testing
 */
export const testKnowledgeApi = async () => {
  try {
    console.log('Testing Knowledge Management API...');

    // Test creating a knowledge area
    console.log('1. Creating a knowledge area...');
    const newArea = await apiService.createKnowledgeArea({
      name: 'Test Area',
      description: 'This is a test knowledge area'
    });
    console.log('Created area:', newArea);

    // Test creating a knowledge topic
    console.log('2. Creating a knowledge topic...');
    const newTopic = await apiService.createKnowledgeTopic(newArea.id, {
      name: 'Test Topic',
      description: 'This is a test knowledge topic'
    });
    console.log('Created topic:', newTopic);

    // Test creating a knowledge unit
    console.log('3. Creating a knowledge unit...');
    const newUnit = await apiService.createKnowledgeUnit(newTopic.id, {
      name: 'Test Unit',
      description: 'This is a test knowledge unit'
    });
    console.log('Created unit:', newUnit);

    // Test fetching knowledge areas
    console.log('4. Fetching all knowledge areas...');
    const areas = await apiService.getKnowledgeAreas();
    console.log('All areas:', areas);

    // Test fetching knowledge topics
    console.log('5. Fetching topics for the created area...');
    const topics = await apiService.getKnowledgeTopics(newArea.id);
    console.log('Topics for area:', topics);

    // Test fetching knowledge units
    console.log('6. Fetching units for the created topic...');
    const units = await apiService.getKnowledgeUnits(newTopic.id);
    console.log('Units for topic:', units);

    console.log('✅ All knowledge API tests completed successfully!');
    
    return {
      area: newArea,
      topic: newTopic,
      unit: newUnit,
      allAreas: areas,
      allTopics: topics,
      allUnits: units
    };
  } catch (error) {
    console.error('❌ Knowledge API test failed:', error);
    throw error;
  }
};

/**
 * Test function specifically for the handleAddItem functionality
 */
export const testHandleAddItemApi = async () => {
  try {
    console.log('Testing handleAddItem API integration...');

    // Simulate creating an area (like handleAddItem does)
    const areaData = {
      name: 'Frontend Development',
      description: 'Knowledge area for frontend development topics'
    };

    const createdArea = await apiService.createKnowledgeArea(areaData);
    console.log('Area created via handleAddItem simulation:', createdArea);

    // Simulate creating a topic under the area
    const topicData = {
      name: 'React Development',
      description: 'React framework and ecosystem'
    };

    const createdTopic = await apiService.createKnowledgeTopic(createdArea.id, topicData);
    console.log('Topic created via handleAddItem simulation:', createdTopic);

    // Simulate creating a unit under the topic
    const unitData = {
      name: 'React Hooks',
      description: 'Understanding and using React hooks'
    };

    const createdUnit = await apiService.createKnowledgeUnit(createdTopic.id, unitData);
    console.log('Unit created via handleAddItem simulation:', createdUnit);

    console.log('✅ handleAddItem API integration test completed successfully!');
    
    return {
      area: createdArea,
      topic: createdTopic,
      unit: createdUnit
    };
  } catch (error) {
    console.error('❌ handleAddItem API integration test failed:', error);
    throw error;
  }
};

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).testKnowledgeApi = testKnowledgeApi;
  (window as any).testHandleAddItemApi = testHandleAddItemApi;
}
