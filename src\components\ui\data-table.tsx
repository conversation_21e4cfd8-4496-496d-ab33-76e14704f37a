import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Download, Search, SortAsc, SortDesc } from "lucide-react";

interface Column {
  Header: string;
  accessor: string;
  Cell?: ({ row }: { row: any }) => React.ReactNode;
}

interface DataTableProps {
  columns: Column[];
  data: any[];
  filter?: Record<string, string>;
  onFilterChange?: (filter: Record<string, string>) => void;
  sortableColumns?: string[];
  filterableColumns?: string[];
  searchable?: boolean;
  exportable?: boolean;
}

const DataTable: React.FC<DataTableProps> = ({
  columns,
  data,
  filter = {},
  onFilterChange,
  sortableColumns = [],
  filterableColumns = [],
  searchable = false,
  exportable = false,
}) => {
  const [sortConfig, setSortConfig] = React.useState<{
    key: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [searchTerm, setSearchTerm] = React.useState("");

  // Apply sorting
  const sortedData = React.useMemo(() => {
    let sortableData = [...data];
    if (sortConfig !== null) {
      sortableData.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === "asc" ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableData;
  }, [data, sortConfig]);

  // Apply filtering
  const filteredData = React.useMemo(() => {
    return sortedData.filter((row) => {
      // Apply search filter
      if (searchTerm && searchable) {
        const searchMatch = Object.values(row).some(
          (value) =>
            value &&
            value.toString().toLowerCase().includes(searchTerm.toLowerCase())
        );
        if (!searchMatch) return false;
      }

      // Apply column filters
      if (filterableColumns.length > 0 && Object.keys(filter).length > 0) {
        return Object.entries(filter).every(([key, value]) => {
          if (!value) return true;
          return (
            row[key] &&
            row[key].toString().toLowerCase().includes(value.toLowerCase())
          );
        });
      }

      return true;
    });
  }, [sortedData, searchTerm, filter, filterableColumns, searchable]);

  // Handle sort
  const handleSort = (key: string) => {
    if (!sortableColumns.includes(key)) return;
    
    setSortConfig((prevSortConfig) => {
      if (prevSortConfig && prevSortConfig.key === key) {
        return prevSortConfig.direction === "asc"
          ? { key, direction: "desc" }
          : null;
      }
      return { key, direction: "asc" };
    });
  };

  // Handle filter change
  const handleFilterChange = (key: string, value: string) => {
    if (onFilterChange) {
      onFilterChange({ ...filter, [key]: value });
    }
  };

  // Handle export
  const handleExport = () => {
    if (!exportable) return;

    // Convert data to CSV
    const headers = columns.map((column) => column.Header);
    const accessors = columns.map((column) => column.accessor);
    
    // Filter out action columns
    const csvHeaders = headers.filter((_, index) => accessors[index] !== "actions");
    const csvAccessors = accessors.filter((accessor) => accessor !== "actions");
    
    const csvContent = [
      csvHeaders.join(","),
      ...filteredData.map((row) =>
        csvAccessors.map((accessor) => JSON.stringify(row[accessor] || "")).join(",")
      ),
    ].join("\n");

    // Create and download CSV file
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", "export.csv");
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        {searchable && (
          <div className="relative w-full sm:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        )}
        <div className="flex gap-2 ml-auto">
          {exportable && (
            <Button variant="outline" size="sm" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </div>

      {filterableColumns.length > 0 && (
        <div className="flex flex-wrap gap-4">
          {filterableColumns.map((key) => (
            <div key={key} className="w-full sm:w-auto">
              <Input
                placeholder={`Filter by ${key}`}
                value={filter[key] || ""}
                onChange={(e) => handleFilterChange(key, e.target.value)}
                className="w-full"
              />
            </div>
          ))}
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column.accessor}>
                  <div className="flex items-center gap-1">
                    {column.Header}
                    {sortableColumns.includes(column.accessor) && (
                      <button
                        onClick={() => handleSort(column.accessor)}
                        className="ml-1 p-0.5 rounded-sm hover:bg-muted focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        {sortConfig?.key === column.accessor ? (
                          sortConfig.direction === "asc" ? (
                            <SortAsc className="h-4 w-4" />
                          ) : (
                            <SortDesc className="h-4 w-4" />
                          )
                        ) : (
                          <div className="h-4 w-4" />
                        )}
                      </button>
                    )}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results found.
                </TableCell>
              </TableRow>
            ) : (
              filteredData.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map((column) => (
                    <TableCell key={`${rowIndex}-${column.accessor}`}>
                      {column.Cell
                        ? column.Cell({ row })
                        : row[column.accessor]}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default DataTable;