// Import the required PDF libraries
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';

// Mock types to simulate the real libraries
interface TrainingAssignment {
  id: string;
  employeeName?: string;
  trainingProgram: string;
  businessUnit: string;
  departmentGroup: string;
  department: string;
  division: string;
  subDivision: string;
  category: string;
  grade: string;
  designation: string;
  status: string;
  assignedDate: string;
  dueDate?: string;
  notes?: string;
}

interface User {
  id: string;
  name: string;
  employeeNo: string;
  email: string;
  department: string;
  division: string;
  type: string;
}

// Interface for unit hierarchy
interface UnitHierarchy {
  area: string;
  topic: string;
  unit: string;
}

/**
 * Generates a PDF document for a training assignment
 *
 * @param assignment The training assignment details
 * @param users List of users assigned to the training
 * @param extractedUnits List of training units
 * @param unitHierarchy Hierarchical structure of units with their areas and topics
 */
export const generateTrainingAssignmentPDF = (
  assignment: TrainingAssignment,
  users: User[],
  extractedUnits: string[],
  unitHierarchy: { area: string; topic: string; unit: string }[]
) => {
  // Create a new PDF document
  const doc = new jsPDF();

  // Define colors to match the platform's color scheme
  const primaryColor = [236, 72, 153]; // Pink/magenta color from the platform
  const secondaryColor = [147, 51, 234]; // Purple from the platform
  const textColor = [33, 33, 33]; // Dark gray
  const lightGray = [224, 224, 224]; // Light gray for borders
  const bgColor = [249, 250, 251]; // Light background color

  // Set up document properties
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 10;

  // Create a clean, modern header
  // Add a subtle background color to the entire page
  doc.setFillColor(bgColor[0], bgColor[1], bgColor[2]);
  doc.rect(0, 0, pageWidth, pageHeight, 'F');

  // Add header bar
  doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.rect(0, 0, pageWidth, 25, 'F');

  // Add accent line
  doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.rect(0, 25, pageWidth, 2, 'F');

  // Add company logo text (in a real implementation, you would add an actual logo image)
  doc.setTextColor(255, 255, 255); // White text for header
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('KOACH', 15, 15);

  // Add title
  doc.setFontSize(14);
  doc.text('Training Assignment Details', pageWidth / 2, 15, { align: 'center' });

  // Add decorative element on the right side of header
  doc.setFillColor(255, 255, 255, 0.2); // Semi-transparent white
  doc.circle(pageWidth - 15, 12, 5, 'F');

  // Reset text color for the rest of the document
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);
  doc.setFont('helvetica', 'normal');

  // Add current date with a more modern style
  const today = new Date();
  const formattedDate = today.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Add a document info section
  doc.setFillColor(245, 245, 245);
  doc.roundedRect(margin, 35, pageWidth - (margin * 2), 20, 2, 2, 'F');

  doc.setFontSize(9);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated: ${formattedDate}`, margin + 5, 45);

  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.setFontSize(9);
  doc.setFont('helvetica', 'bold');
  doc.text('OFFICIAL DOCUMENT', pageWidth - margin - 5, 45, { align: 'right' });

  // Add assignment information section with a more modern style
  const sectionY = 65;

  // Section title with icon-like element
  doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.circle(margin + 4, sectionY - 5, 2, 'F');

  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.text('Assignment Information', margin + 10, sectionY - 5);

  // Add a subtle line under the section title
  doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.5);
  doc.setLineWidth(0.5);
  doc.line(margin, sectionY, pageWidth - margin, sectionY);

  doc.setFont('helvetica', 'normal');
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);

  // Create a table for assignment information with a modern design
  const assignmentData = [
    ['Business Unit', assignment.businessUnit],
    ['Department Group', assignment.departmentGroup],
    ['Department', assignment.department],
    ['Division', assignment.division],
    ['Sub-Division', assignment.subDivision],
    ['Category', assignment.category],
    ['Grade', assignment.grade],
    ['Designation', assignment.designation]
  ];

  autoTable(doc, {
    startY: sectionY + 5,
    head: [],
    body: assignmentData,
    theme: 'grid',
    styles: {
      fontSize: 9,
      cellPadding: 4,
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
      overflow: 'linebreak', // Ensure text wraps instead of being cut off
      cellWidth: 'wrap',     // Allow cells to expand based on content
    },
    columnStyles: {
      0: {
        fontStyle: 'bold',
        cellWidth: 40,
        fillColor: [250, 250, 250],
        textColor: primaryColor,
      },
      1: {
        cellWidth: 'auto',   // Allow value column to expand as needed
        minCellWidth: 100,   // Ensure minimum width for readability
      }
    },
    margin: { left: margin, right: margin },
    tableWidth: 'auto',
    didDrawCell: (data) => {
      // Add a subtle gradient to header cells
      if (data.column.index === 0) {
        const x = data.cell.x;
        const y = data.cell.y;
        const h = data.cell.height;

        // Draw a subtle left border for the label column
        doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.5);
        doc.setLineWidth(0.5);
        doc.line(x, y, x, y + h);
      }
    }
  });

  // Get the final Y position after the table
  let finalY = doc.lastAutoTable.finalY + 10;

  // Add status information section with a modern style
  // Section title with icon-like element
  doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.circle(margin + 4, finalY + 5, 2, 'F');

  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.text('Status Information', margin + 10, finalY + 5);

  // Add a subtle line under the section title
  doc.setDrawColor(secondaryColor[0], secondaryColor[1], secondaryColor[2], 0.5);
  doc.setLineWidth(0.5);
  doc.line(margin, finalY + 10, pageWidth - margin, finalY + 10);

  doc.setFont('helvetica', 'normal');
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);

  // Create a table for status information with a modern design
  const statusData = [
    ['Status', assignment.status],
    ['Assigned Date', assignment.assignedDate],
    ['Due Date', assignment.dueDate || 'Not specified']
  ];

  if (assignment.notes) {
    statusData.push(['Notes', assignment.notes]);
  }

  autoTable(doc, {
    startY: finalY + 15,
    head: [],
    body: statusData,
    theme: 'grid',
    styles: {
      fontSize: 9,
      cellPadding: 4,
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
      overflow: 'linebreak', // Ensure text wraps instead of being cut off
      cellWidth: 'wrap',     // Allow cells to expand based on content
    },
    columnStyles: {
      0: {
        fontStyle: 'bold',
        cellWidth: 40,
        fillColor: [250, 250, 250],
        textColor: secondaryColor,
      },
      1: {
        cellWidth: 'auto',   // Allow value column to expand as needed
        minCellWidth: 100,   // Ensure minimum width for readability
      }
    },
    margin: { left: margin, right: margin },
    tableWidth: 'auto',
    didDrawCell: (data) => {
      // Add a subtle left border for the label column
      if (data.column.index === 0) {
        const x = data.cell.x;
        const y = data.cell.y;
        const h = data.cell.height;

        doc.setDrawColor(secondaryColor[0], secondaryColor[1], secondaryColor[2], 0.5);
        doc.setLineWidth(0.5);
        doc.line(x, y, x, y + h);
      }
    }
  });

  // Get the final Y position after the table
  finalY = doc.lastAutoTable.finalY + 10;

  // Check if we need a new page for the training program section
  // We want to ensure the entire training program section stays together on one page
  if (finalY > 180) { // Lower threshold to ensure enough space for the section
    doc.addPage();

    // Add header to new page
    doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.rect(0, 0, pageWidth, 15, 'F');

    // Add accent line
    doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
    doc.rect(0, 15, pageWidth, 2, 'F');

    finalY = 30;
  }

  // Add training program section with a modern style
  // Section title with icon-like element
  doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.circle(margin + 4, finalY + 5, 2, 'F');

  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.text('Training Program', margin + 10, finalY + 5);

  // Add a subtle line under the section title
  doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.5);
  doc.setLineWidth(0.5);
  doc.line(margin, finalY + 10, pageWidth - margin, finalY + 10);

  doc.setFont('helvetica', 'normal');
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);

  finalY += 15;

  if (assignment.trainingProgram === "All Units Selected") {
    // Create a bordered box instead of filled background
    doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.5);
    doc.setLineWidth(0.5);
    doc.roundedRect(margin, finalY, pageWidth - (margin * 2), 20, 2, 2, 'S');

    // Add a small icon/indicator
    doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.circle(margin + 15, finalY + 10, 2, 'F');

    doc.setFontSize(10);
    doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.setFont('helvetica', 'bold');
    doc.text('All Units Selected', margin + 25, finalY + 12);

    finalY += 25;
  } else {
    // Group units by area and topic
    const groupedUnits: Record<string, Record<string, string[]>> = {};

    unitHierarchy.forEach(({ area, topic, unit }) => {
      if (!groupedUnits[area]) {
        groupedUnits[area] = {};
      }

      if (!groupedUnits[area][topic]) {
        groupedUnits[area][topic] = [];
      }

      groupedUnits[area][topic].push(unit);
    });

    // Create a modern table for units with better styling
    const unitsData: string[][] = [];

    Object.entries(groupedUnits).forEach(([area, topics]) => {
      Object.entries(topics).forEach(([topic, units]) => {
        // Add area and topic as a header row
        unitsData.push([`${area} → ${topic}`, '']);

        // Add each unit
        units.forEach(unit => {
          unitsData.push(['', unit]);
        });
      });
    });

    // Instead of using a table, let's create a more visually appealing layout for Area, Topic, and Units
    let currentY = finalY;
    const areaTopicGap = 5;
    const unitIndent = 15;

    Object.entries(groupedUnits).forEach(([area, topics], areaIndex) => {
      // For each Area
      Object.entries(topics).forEach(([topic, units], topicIndex) => {
        // Calculate height needed for this section
        const boxHeight = 40 + (units.length * 12); // Header + units label + units

        // Check if we need a page break
        if (currentY + boxHeight > pageHeight - 30) {
          doc.addPage();

          // Add header to new page
          doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
          doc.rect(0, 0, pageWidth, 15, 'F');

          // Add accent line
          doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
          doc.rect(0, 15, pageWidth, 2, 'F');

          currentY = 30;
        }

        // Draw a light border instead of a filled box
        doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.3);
        doc.setLineWidth(0.5);
        doc.roundedRect(margin, currentY, pageWidth - (margin * 2), boxHeight, 3, 3, 'S');

        // Add Area header with better styling
        doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.1);
        doc.roundedRect(margin + 5, currentY + 5, 80, 15, 2, 2, 'F');

        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
        doc.text(`Area: ${area}`, margin + 10, currentY + 15);

        // Add Topic header with better styling
        doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2], 0.1);
        doc.roundedRect(margin + 90, currentY + 5, 80, 15, 2, 2, 'F');

        doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        doc.text(`Topic: ${topic}`, margin + 95, currentY + 15);

        // Add a separator line
        doc.setDrawColor(220, 220, 220);
        doc.setLineWidth(0.5);
        doc.line(margin + 10, currentY + 25, pageWidth - margin - 10, currentY + 25);

        // Add "Units:" label
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(80, 80, 80);
        doc.setFontSize(9);
        doc.text("Units:", margin + 10, currentY + 35);

        // Add units with better styling
        doc.setFont('helvetica', 'normal');
        doc.setTextColor(80, 80, 80);
        doc.setFontSize(9);

        units.forEach((unit, unitIndex) => {
          const unitY = currentY + 45 + (unitIndex * 12);

          // Create a pill-shaped background for each unit
          doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.1);
          const textWidth = doc.getTextWidth(unit) + 10;
          doc.roundedRect(margin + unitIndent, unitY - 5, textWidth, 10, 5, 5, 'F');

          // Draw unit text
          doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
          doc.text(unit, margin + unitIndent + 5, unitY);
        });

        // Update Y position for next area-topic group
        currentY += boxHeight + 10;
      });
    });

    // Update finalY for next section
    finalY = currentY;
  }

  // Check if we need a new page for the users section
  if (finalY > 230) {
    doc.addPage();

    // Add header to new page
    doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.rect(0, 0, pageWidth, 15, 'F');

    // Add accent line
    doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
    doc.rect(0, 15, pageWidth, 2, 'F');

    finalY = 30;
  }

  // Add assigned users section with a modern style
  // Section title with icon-like element
  doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.circle(margin + 4, finalY + 5, 2, 'F');

  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.text('Assigned Users', margin + 10, finalY + 5);

  // Add a subtle line under the section title
  doc.setDrawColor(secondaryColor[0], secondaryColor[1], secondaryColor[2], 0.5);
  doc.setLineWidth(0.5);
  doc.line(margin, finalY + 10, pageWidth - margin, finalY + 10);

  doc.setFont('helvetica', 'normal');
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);

  // Create table for users with modern styling
  const userTableData = users.map(user => [
    user.name,
    user.employeeNo,
    user.email,
    user.department,
    user.division,
    user.type
  ]);

  autoTable(doc, {
    startY: finalY + 15,
    head: [['Name', 'Employee ID', 'Email', 'Department', 'Division', 'Type']],
    body: userTableData,
    theme: 'grid',
    styles: {
      fontSize: 8,
      cellPadding: 4,
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
      overflow: 'linebreak', // Ensure text wraps instead of being cut off
      cellWidth: 'wrap',     // Allow cells to expand based on content
    },
    headStyles: {
      fillColor: secondaryColor,
      textColor: [255, 255, 255],
      fontStyle: 'bold',
      halign: 'center',
    },
    columnStyles: {
      0: { cellWidth: 'auto' }, // Name
      1: { cellWidth: 25 },     // Employee ID
      2: { cellWidth: 'auto' }, // Email
      3: { cellWidth: 'auto' }, // Department
      4: { cellWidth: 'auto' }, // Division
      5: { cellWidth: 25 }      // Type
    },
    alternateRowStyles: {
      fillColor: [250, 250, 250]
    },
    margin: { left: margin, right: margin },
    // Ensure the table doesn't get split across pages
    pageBreak: 'avoid',
    // If the table is too large for one page, add a header to the next page
    didDrawPage: (data) => {
      // If we're on a new page, add the header again
      if (data.cursor.y === margin) {
        // Add header to new page
        doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
        doc.rect(0, 0, pageWidth, 15, 'F');

        // Add accent line
        doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        doc.rect(0, 15, pageWidth, 2, 'F');
      }
    }
  });

  // Add watermark and footer to each page
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);

    // Removed watermark as requested

    // Add footer background
    doc.setFillColor(248, 249, 250);
    doc.rect(0, pageHeight - 20, pageWidth, 20, 'F');

    // Add footer line
    doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.3);
    doc.setLineWidth(0.5);
    doc.line(margin, pageHeight - 20, pageWidth - margin, pageHeight - 20);

    // Add footer with page numbers
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 100, 100);
    doc.text(
      `Page ${i} of ${pageCount}`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 10,
      { align: 'center' }
    );

    // Add company info in footer with logo-like element
    doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.circle(margin + 3, pageHeight - 10, 2, 'F');

    doc.setTextColor(80, 80, 80);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    doc.text(
      'KOACH Training Management System',
      margin + 8,
      doc.internal.pageSize.height - 10
    );

    // Add date in footer
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 100, 100);
    doc.text(
      formattedDate,
      doc.internal.pageSize.width - margin,
      doc.internal.pageSize.height - 10,
      { align: 'right' }
    );

    // Add a QR code placeholder in the bottom right (in a real implementation, you would generate an actual QR code)
    doc.setFillColor(240, 240, 240);
    doc.roundedRect(pageWidth - margin - 20, pageHeight - 18, 15, 15, 1, 1, 'F');

    // Add verification text
    doc.setFontSize(6);
    doc.setTextColor(150, 150, 150);
    doc.text(
      'Scan to verify',
      pageWidth - margin - 12.5,
      pageHeight - 20,
      { align: 'center' }
    );
  }

  // Save the PDF with a more descriptive filename
  const filename = `Training_Assignment_${assignment.id}_${formattedDate.replace(/,\s/g, '_').replace(/\s/g, '_')}.pdf`;
  doc.save(filename);
};

/**
 * Generates a PDF document for multiple training assignments
 *
 * @param assignments The list of training assignments to export
 * @param areas List of all areas
 * @param topics Map of topics by area
 * @param units Map of units by topic
 */
export const exportTrainingAssignmentsToPDF = (
  assignments: TrainingAssignment[],
  areas: string[],
  topics: Record<string, string[]>,
  units: Record<string, string[]>
) => {
  // Create a new PDF document
  const doc = new jsPDF();

  // Define colors to match the platform's color scheme
  const primaryColor = [236, 72, 153]; // Pink/magenta color from the platform
  const secondaryColor = [147, 51, 234]; // Purple from the platform
  const textColor = [33, 33, 33]; // Dark gray
  const lightGray = [224, 224, 224]; // Light gray for borders
  const bgColor = [249, 250, 251]; // Light background color

  // Set up document properties
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;
  const margin = 10;

  // Create a clean, modern header
  // Add a subtle background color to the entire page
  doc.setFillColor(bgColor[0], bgColor[1], bgColor[2]);
  doc.rect(0, 0, pageWidth, pageHeight, 'F');

  // Add header bar
  doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.rect(0, 0, pageWidth, 25, 'F');

  // Add accent line
  doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.rect(0, 25, pageWidth, 2, 'F');

  // Add company logo text (in a real implementation, you would add an actual logo image)
  doc.setTextColor(255, 255, 255); // White text for header
  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('KOACH', 15, 15);

  // Add title
  doc.setFontSize(14);
  doc.text('Employee Training Assignments', pageWidth / 2, 15, { align: 'center' });

  // Add decorative element on the right side of header
  doc.setFillColor(255, 255, 255, 0.2); // Semi-transparent white
  doc.circle(pageWidth - 15, 12, 5, 'F');

  // Reset text color for the rest of the document
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);
  doc.setFont('helvetica', 'normal');

  // Add current date with a more modern style
  const today = new Date();
  const formattedDate = today.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Add a document info section
  doc.setFillColor(245, 245, 245);
  doc.roundedRect(margin, 35, pageWidth - (margin * 2), 20, 2, 2, 'F');

  doc.setFontSize(9);
  doc.setTextColor(100, 100, 100);
  doc.text(`Generated: ${formattedDate}`, margin + 5, 45);

  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.setFontSize(9);
  doc.setFont('helvetica', 'bold');
  doc.text('OFFICIAL DOCUMENT', pageWidth - margin - 5, 45, { align: 'right' });

  // Add summary section
  const summaryY = 65;

  // Section title with icon-like element
  doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.circle(margin + 4, summaryY - 5, 2, 'F');

  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(primaryColor[0], primaryColor[1], primaryColor[2]);
  doc.text('Summary', margin + 10, summaryY - 5);

  // Add a subtle line under the section title
  doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.5);
  doc.setLineWidth(0.5);
  doc.line(margin, summaryY, pageWidth - margin, summaryY);

  doc.setFont('helvetica', 'normal');
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);

  // Create a summary table
  const summaryData = [
    ['Total Assignments', assignments.length.toString()],
    ['Status Breakdown', ''],
  ];

  // Count assignments by status
  const statusCounts: Record<string, number> = {};
  assignments.forEach(assignment => {
    statusCounts[assignment.status] = (statusCounts[assignment.status] || 0) + 1;
  });

  // Add status counts to summary
  Object.entries(statusCounts).forEach(([status, count]) => {
    summaryData.push(['', `${status}: ${count}`]);
  });

  autoTable(doc, {
    startY: summaryY + 5,
    head: [],
    body: summaryData,
    theme: 'grid',
    styles: {
      fontSize: 9,
      cellPadding: 4,
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
    },
    columnStyles: {
      0: {
        fontStyle: 'bold',
        cellWidth: 40,
        fillColor: [250, 250, 250],
        textColor: primaryColor,
      },
      1: {
        cellWidth: 'auto',
      }
    },
    margin: { left: margin, right: margin },
  });

  // Get the final Y position after the table
  let finalY = doc.lastAutoTable.finalY + 15;

  // Add assignments table section
  // Section title with icon-like element
  doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.circle(margin + 4, finalY, 2, 'F');

  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.setTextColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
  doc.text('Training Assignments', margin + 10, finalY);

  // Add a subtle line under the section title
  doc.setDrawColor(secondaryColor[0], secondaryColor[1], secondaryColor[2], 0.5);
  doc.setLineWidth(0.5);
  doc.line(margin, finalY + 5, pageWidth - margin, finalY + 5);

  doc.setFont('helvetica', 'normal');
  doc.setTextColor(textColor[0], textColor[1], textColor[2]);

  // Create table for assignments with modern styling
  const assignmentTableData = assignments.map(assignment => [
    assignment.id,
    assignment.trainingProgram.length > 30
      ? assignment.trainingProgram.substring(0, 30) + '...'
      : assignment.trainingProgram,
    assignment.businessUnit,
    assignment.departmentGroup,
    assignment.department,
    assignment.designation,
    assignment.status,
    assignment.assignedDate,
    assignment.dueDate || 'N/A'
  ]);

  autoTable(doc, {
    startY: finalY + 10,
    head: [['ID', 'Training Program', 'Business Unit', 'Dept. Group', 'Department', 'Designation', 'Status', 'Assigned Date', 'Due Date']],
    body: assignmentTableData,
    theme: 'grid',
    styles: {
      fontSize: 8,
      cellPadding: 4,
      lineColor: [220, 220, 220],
      lineWidth: 0.1,
      overflow: 'linebreak',
      cellWidth: 'wrap',
    },
    headStyles: {
      fillColor: secondaryColor,
      textColor: [255, 255, 255],
      fontStyle: 'bold',
      halign: 'center',
    },
    columnStyles: {
      0: { cellWidth: 15 },      // ID
      1: { cellWidth: 'auto' },  // Training Program
      2: { cellWidth: 25 },      // Business Unit
      3: { cellWidth: 25 },      // Dept. Group
      4: { cellWidth: 25 },      // Department
      5: { cellWidth: 25 },      // Designation
      6: { cellWidth: 20 },      // Status
      7: { cellWidth: 25 },      // Assigned Date
      8: { cellWidth: 20 }       // Due Date
    },
    alternateRowStyles: {
      fillColor: [250, 250, 250]
    },
    margin: { left: margin, right: margin },
    didDrawPage: (data) => {
      // If we're on a new page, add the header again
      if (data.cursor.y === margin) {
        // Add header to new page
        doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
        doc.rect(0, 0, pageWidth, 15, 'F');

        // Add accent line
        doc.setFillColor(secondaryColor[0], secondaryColor[1], secondaryColor[2]);
        doc.rect(0, 15, pageWidth, 2, 'F');
      }
    }
  });

  // Add watermark and footer to each page
  const pageCount = doc.internal.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);

    // Add footer background
    doc.setFillColor(248, 249, 250);
    doc.rect(0, pageHeight - 20, pageWidth, 20, 'F');

    // Add footer line
    doc.setDrawColor(primaryColor[0], primaryColor[1], primaryColor[2], 0.3);
    doc.setLineWidth(0.5);
    doc.line(margin, pageHeight - 20, pageWidth - margin, pageHeight - 20);

    // Add footer with page numbers
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 100, 100);
    doc.text(
      `Page ${i} of ${pageCount}`,
      doc.internal.pageSize.width / 2,
      doc.internal.pageSize.height - 10,
      { align: 'center' }
    );

    // Add company info in footer with logo-like element
    doc.setFillColor(primaryColor[0], primaryColor[1], primaryColor[2]);
    doc.circle(margin + 3, pageHeight - 10, 2, 'F');

    doc.setTextColor(80, 80, 80);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    doc.text(
      'KOACH Training Management System',
      margin + 8,
      doc.internal.pageSize.height - 10
    );

    // Add date in footer
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100, 100, 100);
    doc.text(
      formattedDate,
      doc.internal.pageSize.width - margin,
      doc.internal.pageSize.height - 10,
      { align: 'right' }
    );
  }

  // Save the PDF with a descriptive filename
  const filename = `Training_Assignments_${formattedDate.replace(/,\s/g, '_').replace(/\s/g, '_')}.pdf`;
  doc.save(filename);
};

/**
 * Exports training assignments to Excel format with enhanced formatting
 *
 * @param assignments The list of training assignments to export
 */
export const exportTrainingAssignmentsToExcel = (
  assignments: TrainingAssignment[]
) => {
  // Create worksheet data
  const worksheetData = [
    // Header row
    [
      'ID',
      'Training Program',
      'Business Unit',
      'Department Group',
      'Department',
      'Division',
      'Sub-Division',
      'Category',
      'Grade',
      'Designation',
      'Status',
      'Assigned Date',
      'Due Date',
      'Notes'
    ]
  ];

  // Add data rows
  assignments.forEach(assignment => {
    worksheetData.push([
      assignment.id,
      assignment.trainingProgram,
      assignment.businessUnit,
      assignment.departmentGroup,
      assignment.department,
      assignment.division,
      assignment.subDivision,
      assignment.category,
      assignment.grade,
      assignment.designation,
      assignment.status,
      assignment.assignedDate,
      assignment.dueDate || '',
      assignment.notes || ''
    ]);
  });

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths
  const columnWidths = [
    { wch: 5 },    // ID
    { wch: 40 },   // Training Program
    { wch: 15 },   // Business Unit
    { wch: 20 },   // Department Group
    { wch: 20 },   // Department
    { wch: 20 },   // Division
    { wch: 20 },   // Sub-Division
    { wch: 15 },   // Category
    { wch: 15 },   // Grade
    { wch: 20 },   // Designation
    { wch: 15 },   // Status
    { wch: 15 },   // Assigned Date
    { wch: 15 },   // Due Date
    { wch: 30 }    // Notes
  ];
  worksheet['!cols'] = columnWidths;

  // Apply formatting to header row
  // Define header style with bold font and larger font size
  const headerStyle = {
    font: {
      bold: true,
      sz: 12, // Font size 12pt
      color: { rgb: "000000" }
    },
    fill: {
      fgColor: { rgb: "E9E9E9" } // Light gray background
    },
    alignment: {
      horizontal: "center",
      vertical: "center"
    },
    border: {
      top: { style: "thin" },
      bottom: { style: "thin" },
      left: { style: "thin" },
      right: { style: "thin" }
    }
  };

  // Apply header style to each cell in the header row
  const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
  for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
    if (!worksheet[cellRef]) continue;

    worksheet[cellRef].s = headerStyle;
  }

  // Apply alternating row colors for better readability
  const dataStyle = {
    alignment: {
      vertical: "center",
      wrapText: true
    }
  };

  const alternateRowStyle = {
    ...dataStyle,
    fill: {
      fgColor: { rgb: "F5F5F5" } // Very light gray for alternating rows
    }
  };

  // Apply styles to data rows
  for (let row = 1; row <= headerRange.e.r; row++) {
    const rowStyle = row % 2 === 0 ? alternateRowStyle : dataStyle;

    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
      const cellRef = XLSX.utils.encode_cell({ r: row, c: col });
      if (!worksheet[cellRef]) continue;

      worksheet[cellRef].s = rowStyle;
    }
  }

  // Create workbook
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Training Assignments');

  // Generate filename
  const today = new Date();
  const formattedDate = today.toISOString().split('T')[0];
  const filename = `Training_Assignments_${formattedDate}.xlsx`;

  // Save file
  XLSX.writeFile(workbook, filename);
};
