
import React, { useState } from "react";
import { motion } from "framer-motion";

// Import enhanced components
import InsightsHeader from "@/components/insights/InsightsHeader";
import EnhancedKnowledgeSelectionFilters from "@/components/insights/EnhancedKnowledgeSelectionFilters";
import KnowledgeIndexCalculation from "@/components/insights/KnowledgeIndexCalculation";
import EnhancedKnowledgeIndexDistribution from "@/components/insights/EnhancedKnowledgeIndexDistribution";
import EnhancedKnowledgeIndexTrend from "@/components/insights/EnhancedKnowledgeIndexTrend";
import KnowledgePullTrends from "@/components/insights/KnowledgePullTrends";
import UserPerformanceMetrics from "@/components/insights/UserPerformanceMetrics";

export default function Insights() {
  const [refreshKey, setRefreshKey] = useState(0);
  const [selection, setSelection] = useState<{
    area: string | null;
    topic: string | null;
    units: string[];
  }>({
    area: null,
    topic: null,
    units: [],
  });

  const handleSelectionChange = (newSelection: {
    area: string | null;
    topic: string | null;
    units: string[];
  }) => {
    setSelection(newSelection);
  };

  // Handler for refreshing data
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  // Handler for filter changes
  const handleFilterChange = (period: string) => {
    console.log(`Filter changed to: ${period}`);
    // In a real app, you would fetch new data based on the period
  };

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="space-y-8"
      key={refreshKey}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Insights Header */}
      <InsightsHeader
        title="Insights"
        subtitle="Analyze and understand your knowledge management metrics"
        onRefresh={handleRefresh}
        onExport={() => console.log('Exporting report...')}
        onFilterChange={handleFilterChange}
      />

      {/* Knowledge Selection Filters */}
      <EnhancedKnowledgeSelectionFilters onSelectionChange={handleSelectionChange} />

      {/* Knowledge Index Calculation */}
      <KnowledgeIndexCalculation selection={selection} />

      {/* Knowledge Index Distribution and Trend */}
      <motion.div
        className="grid grid-cols-1 lg:grid-cols-2 gap-8"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants} className="h-full">
          <EnhancedKnowledgeIndexDistribution selection={selection} />
        </motion.div>

        <motion.div variants={itemVariants} className="h-full">
          <EnhancedKnowledgeIndexTrend selection={selection} />
        </motion.div>
      </motion.div>

      {/* Knowledge Pull Trends with animation */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <KnowledgePullTrends selection={selection} />
        </motion.div>
      </motion.div>

      {/* User Performance Metrics with animation */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <UserPerformanceMetrics selection={selection} />
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
