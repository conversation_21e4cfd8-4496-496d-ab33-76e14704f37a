import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { X, Check } from "lucide-react";
import { businessUnits, departmentGroups, departments, divisions, subDivisions, categories, grades, designations } from "@/data/organizationData";
import { MultiSelectCheckbox } from "@/components/MultiSelectCheckbox";
import { OrganizationTable } from "@/components/OrganizationTable";

interface SelectOrganizationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  }) => void;
  initialSelection?: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  };
}

export function SelectOrganizationDialog({
  open,
  onOpenChange,
  onConfirm,
  initialSelection = {
    businessUnits: [],
    departmentGroups: [],
    departments: [],
    divisions: [],
    subDivisions: [],
    categories: [],
    grades: [],
    designations: [],
  },
}: SelectOrganizationDialogProps) {
  const [selection, setSelection] = useState(initialSelection);

  // Available options based on selections
  const [availableDepartmentGroups, setAvailableDepartmentGroups] = useState<string[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<string[]>(Object.keys(departments));
  const [availableDivisions, setAvailableDivisions] = useState<string[]>([]);
  const [availableSubDivisions, setAvailableSubDivisions] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableGrades, setAvailableGrades] = useState<string[]>([]);
  const [availableDesignations, setAvailableDesignations] = useState<string[]>([]);

  // Update available department groups when business units change
  useEffect(() => {
    if (selection.businessUnits.length > 0) {
      const allDepartmentGroups = selection.businessUnits.flatMap(
        bu => departmentGroups[bu] || []
      );
      setAvailableDepartmentGroups([...new Set(allDepartmentGroups)]);
    } else {
      setAvailableDepartmentGroups([]);
    }
  }, [selection.businessUnits]);

  // Update available divisions when departments change
  useEffect(() => {
    if (selection.departments.length > 0) {
      const allDivisions = selection.departments.flatMap(
        dept => divisions[dept] || []
      );
      setAvailableDivisions([...new Set(allDivisions)]);
    } else {
      setAvailableDivisions([]);
    }
  }, [selection.departments]);

  // Update available sub-divisions when divisions change
  useEffect(() => {
    if (selection.divisions.length > 0) {
      const allSubDivisions = selection.divisions.flatMap(
        div => subDivisions[div] || []
      );
      setAvailableSubDivisions([...new Set(allSubDivisions)]);
    } else {
      setAvailableSubDivisions([]);
    }
  }, [selection.divisions]);

  // Update available categories when sub-divisions change
  useEffect(() => {
    if (selection.subDivisions.length > 0) {
      const allCategories = selection.subDivisions.flatMap(
        subdiv => categories[subdiv] || []
      );
      setAvailableCategories([...new Set(allCategories)]);
    } else {
      setAvailableCategories([]);
    }
  }, [selection.subDivisions]);

  // Update available grades when categories change
  useEffect(() => {
    if (selection.categories.length > 0) {
      const allGrades = selection.categories.flatMap(
        cat => grades[cat] || []
      );
      setAvailableGrades([...new Set(allGrades)]);
    } else {
      setAvailableGrades([]);
    }
  }, [selection.categories]);

  // Update available designations when grades change
  useEffect(() => {
    if (selection.grades.length > 0) {
      const allDesignations = selection.grades.flatMap(
        grade => designations[grade] || []
      );
      setAvailableDesignations([...new Set(allDesignations)]);
    } else {
      setAvailableDesignations([]);
    }
  }, [selection.grades]);

  // Toggle selection for business units
  const toggleBusinessUnit = (bu: string) => {
    setSelection(prev => {
      const isSelected = prev.businessUnits.includes(bu);
      const newBusinessUnits = isSelected
        ? prev.businessUnits.filter(item => item !== bu)
        : [...prev.businessUnits, bu];

      return {
        ...prev,
        businessUnits: newBusinessUnits
      };
    });
  };

  // Toggle selection for department groups
  const toggleDepartmentGroup = (dg: string) => {
    setSelection(prev => {
      const isSelected = prev.departmentGroups.includes(dg);
      const newDepartmentGroups = isSelected
        ? prev.departmentGroups.filter(item => item !== dg)
        : [...prev.departmentGroups, dg];

      return {
        ...prev,
        departmentGroups: newDepartmentGroups
      };
    });
  };

  // Toggle all business units
  const toggleAllBusinessUnits = () => {
    setSelection(prev => {
      if (prev.businessUnits.length === businessUnits.length) {
        return { ...prev, businessUnits: [] };
      } else {
        return { ...prev, businessUnits: [...businessUnits] };
      }
    });
  };

  // Toggle all department groups
  const toggleAllDepartmentGroups = () => {
    setSelection(prev => {
      if (prev.departmentGroups.length === availableDepartmentGroups.length) {
        return { ...prev, departmentGroups: [] };
      } else {
        return { ...prev, departmentGroups: [...availableDepartmentGroups] };
      }
    });
  };

  // Clear all selections
  const clearAll = () => {
    setSelection({
      businessUnits: [],
      departmentGroups: [],
      departments: [],
      divisions: [],
      subDivisions: [],
      categories: [],
      grades: [],
      designations: [],
    });
  };

  // Handle department selection
  const handleDepartmentChange = (values: string[]) => {
    setSelection(prev => ({
      ...prev,
      departments: values
    }));
  };

  // Handle division selection
  const handleDivisionChange = (values: string[]) => {
    setSelection(prev => ({
      ...prev,
      divisions: values
    }));
  };

  // Handle sub-division selection
  const handleSubDivisionChange = (values: string[]) => {
    setSelection(prev => ({
      ...prev,
      subDivisions: values
    }));
  };

  // Handle category selection
  const handleCategoryChange = (values: string[]) => {
    setSelection(prev => ({
      ...prev,
      categories: values
    }));
  };

  // Handle grade selection
  const handleGradeChange = (values: string[]) => {
    setSelection(prev => ({
      ...prev,
      grades: values
    }));
  };

  // Handle designation selection
  const handleDesignationChange = (values: string[]) => {
    setSelection(prev => ({
      ...prev,
      designations: values || []
    }));
  };

  const handleConfirm = () => {
    onConfirm(selection);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold">Select Organization</DialogTitle>
        </DialogHeader>

        <div className="text-sm text-muted-foreground mb-4">
          Select from the organizational hierarchy to target your broadcast.
        </div>

        <div className="grid grid-cols-2 gap-6">
          {/* Business Units */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium">Business Units</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelection(prev => ({ ...prev, businessUnits: [] }))}
                className="h-7 text-xs"
              >
                Clear All
              </Button>
            </div>
            <ScrollArea className="h-[200px] border rounded-md">
              <div className="p-2">
                <div className="flex items-center p-2 hover:bg-muted/50 rounded">
                  <Checkbox
                    id="select-all-business-units"
                    checked={selection.businessUnits.length === businessUnits.length}
                    onCheckedChange={toggleAllBusinessUnits}
                    className="mr-2 h-4 w-4"
                  />
                  <label
                    htmlFor="select-all-business-units"
                    className="text-sm font-medium cursor-pointer flex-grow"
                  >
                    Select all business units
                  </label>
                </div>
                {businessUnits.map(bu => (
                  <div key={bu} className="flex items-center p-2 hover:bg-muted/50 rounded">
                    <Checkbox
                      id={`bu-${bu}`}
                      checked={selection.businessUnits.includes(bu)}
                      onCheckedChange={() => toggleBusinessUnit(bu)}
                      className="mr-2 h-4 w-4"
                    />
                    <label
                      htmlFor={`bu-${bu}`}
                      className="text-sm cursor-pointer flex-grow"
                    >
                      {bu}
                    </label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Department Groups */}
          <div>
            <div className="flex justify-between items-center mb-2">
              <h3 className="font-medium">Department Groups</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelection(prev => ({ ...prev, departmentGroups: [] }))}
                className="h-7 text-xs"
              >
                Clear All
              </Button>
            </div>
            <ScrollArea className="h-[200px] border rounded-md">
              <div className="p-2">
                {availableDepartmentGroups.length > 0 ? (
                  <>
                    <div className="flex items-center p-2 hover:bg-muted/50 rounded">
                      <Checkbox
                        id="select-all-department-groups"
                        checked={selection.departmentGroups.length === availableDepartmentGroups.length}
                        onCheckedChange={toggleAllDepartmentGroups}
                        className="mr-2 h-4 w-4"
                      />
                      <label
                        htmlFor="select-all-department-groups"
                        className="text-sm font-medium cursor-pointer flex-grow"
                      >
                        Select all department groups
                      </label>
                    </div>
                    {availableDepartmentGroups.map(dg => (
                      <div key={dg} className="flex items-center p-2 hover:bg-muted/50 rounded">
                        <Checkbox
                          id={`dg-${dg}`}
                          checked={selection.departmentGroups.includes(dg)}
                          onCheckedChange={() => toggleDepartmentGroup(dg)}
                          className="mr-2 h-4 w-4"
                        />
                        <label
                          htmlFor={`dg-${dg}`}
                          className="text-sm cursor-pointer flex-grow"
                        >
                          {dg}
                        </label>
                      </div>
                    ))}
                  </>
                ) : (
                  <div className="p-4 text-center text-muted-foreground">
                    {selection.businessUnits.length === 0
                      ? "Select Business Units first"
                      : "No Department Groups available"}
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        </div>

        {/* Department, Division, Sub-Division, Category, Grade, Designation */}
        <div className="grid grid-cols-2 gap-6 mt-4">
          <div>
            <MultiSelectCheckbox
              title="Department"
              options={availableDepartments}
              selectedValues={selection.departments}
              onChange={handleDepartmentChange}
              maxHeight="150px"
            />
          </div>

          <div>
            <MultiSelectCheckbox
              title="Division"
              options={availableDivisions}
              selectedValues={selection.divisions}
              onChange={handleDivisionChange}
              disabled={availableDivisions.length === 0}
              maxHeight="150px"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6 mt-4">
          <div>
            <MultiSelectCheckbox
              title="Sub-Division"
              options={availableSubDivisions}
              selectedValues={selection.subDivisions}
              onChange={handleSubDivisionChange}
              disabled={availableSubDivisions.length === 0}
              maxHeight="150px"
            />
          </div>

          <div>
            <MultiSelectCheckbox
              title="Category"
              options={availableCategories}
              selectedValues={selection.categories}
              onChange={handleCategoryChange}
              disabled={availableCategories.length === 0}
              maxHeight="150px"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-6 mt-4">
          <div>
            <MultiSelectCheckbox
              title="Grade"
              options={availableGrades}
              selectedValues={selection.grades}
              onChange={handleGradeChange}
              disabled={availableGrades.length === 0}
              maxHeight="150px"
            />
          </div>

          <div>
            <MultiSelectCheckbox
              title="Designation"
              options={availableDesignations}
              selectedValues={selection.designations}
              onChange={handleDesignationChange}
              disabled={availableDesignations.length === 0}
              maxHeight="150px"
            />
          </div>
        </div>

        {/* Selection Summary */}
        {(selection.businessUnits.length > 0 ||
          selection.departmentGroups.length > 0 ||
          selection.departments.length > 0 ||
          selection.divisions.length > 0 ||
          selection.subDivisions.length > 0 ||
          selection.categories.length > 0 ||
          selection.grades.length > 0 ||
          selection.designations.length > 0) && (
          <div className="mt-6 pt-4 border-t">
            <h3 className="font-medium mb-3 flex items-center text-primary">
              <span className="inline-block w-2 h-2 bg-primary rounded-full mr-2"></span> Selection Summary
            </h3>
            <OrganizationTable selection={selection} />
          </div>
        )}

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleConfirm}>
            Confirm Selection
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Component to display the selected organization items in a hierarchical structure
export function SelectionSummary({
  selection,
  onEdit,
}: {
  selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  };
  onEdit: () => void;
}) {
  return (
    <div className="border rounded-md p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium flex items-center text-primary">
          <span className="inline-block w-2 h-2 bg-primary rounded-full mr-2"></span> Selection Summary
        </h3>
        <Button
          variant="outline"
          size="sm"
          onClick={onEdit}
        >
          Edit Selection
        </Button>
      </div>

      <OrganizationTable selection={selection} />
    </div>
  );
}
