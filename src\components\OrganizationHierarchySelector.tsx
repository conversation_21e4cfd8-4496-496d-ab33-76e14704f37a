import React, { useState, useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { MultiSelectCheckbox } from "@/components/MultiSelectCheckbox";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

// Define the types for our organizational hierarchy
interface OrganizationHierarchy {
  businessUnits: string[];
  departmentGroups: Record<string, string[]>;
  departments: Record<string, string[]>;
  divisions: Record<string, string[]>;
  subDivisions: Record<string, string[]>;
  categories: Record<string, string[]>;
  grades: Record<string, string[]>;
  designations: Record<string, string[]>;
}

// Define the props for our component
interface OrganizationHierarchySelectorProps {
  organizationData: OrganizationHierarchy;
  onChange: (selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  }) => void;
}

export function OrganizationHierarchySelector({
  organizationData,
  onChange,
}: OrganizationHierarchySelectorProps) {
  // State for each level of the hierarchy
  const [selectedBusinessUnits, setSelectedBusinessUnits] = useState<string[]>([]);
  const [selectedDepartmentGroups, setSelectedDepartmentGroups] = useState<string[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);
  const [selectedSubDivisions, setSelectedSubDivisions] = useState<string[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedGrades, setSelectedGrades] = useState<string[]>([]);
  const [selectedDesignations, setSelectedDesignations] = useState<string[]>([]);

  // Available options for each level based on the selections made at higher levels
  const [availableDepartmentGroups, setAvailableDepartmentGroups] = useState<string[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<string[]>([]);
  const [availableDivisions, setAvailableDivisions] = useState<string[]>([]);
  const [availableSubDivisions, setAvailableSubDivisions] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableGrades, setAvailableGrades] = useState<string[]>([]);
  const [availableDesignations, setAvailableDesignations] = useState<string[]>([]);

  // Update available department groups when business units change
  useEffect(() => {
    if (selectedBusinessUnits.length > 0) {
      // Get all department groups for all selected business units
      const allDepartmentGroups = selectedBusinessUnits.flatMap(
        bu => organizationData.departmentGroups[bu] || []
      );
      setAvailableDepartmentGroups([...new Set(allDepartmentGroups)]);
    } else {
      setAvailableDepartmentGroups([]);
      setSelectedDepartmentGroups([]);
    }

    // Reset lower level selections
    setSelectedDepartments([]);
    setSelectedDivisions([]);
    setSelectedSubDivisions([]);
    setSelectedCategories([]);
    setSelectedGrades([]);
    setSelectedDesignations([]);

    // Notify parent component of changes
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: [],
      departments: [],
      divisions: [],
      subDivisions: [],
      categories: [],
      grades: [],
      designations: [],
    });
  }, [selectedBusinessUnits, organizationData.departmentGroups]);

  // Update available departments when department groups change
  useEffect(() => {
    if (selectedDepartmentGroups.length > 0) {
      // Get all departments for all selected department groups
      const allDepartments = selectedDepartmentGroups.flatMap(
        dg => organizationData.departments[dg] || []
      );
      setAvailableDepartments([...new Set(allDepartments)]);
    } else {
      setAvailableDepartments([]);
    }

    // Reset lower level selections
    setSelectedDepartments([]);
    setSelectedDivisions([]);
    setSelectedSubDivisions([]);
    setSelectedCategories([]);
    setSelectedGrades([]);
    setSelectedDesignations([]);

    // Notify parent component of changes
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: selectedDepartmentGroups,
      departments: [],
      divisions: [],
      subDivisions: [],
      categories: [],
      grades: [],
      designations: [],
    });
  }, [selectedDepartmentGroups, organizationData.departments, selectedBusinessUnits]);

  // Update available divisions when departments change
  useEffect(() => {
    if (selectedDepartments.length > 0) {
      const allDivisions = selectedDepartments.flatMap(
        dept => organizationData.divisions[dept] || []
      );
      setAvailableDivisions([...new Set(allDivisions)]);
    } else {
      setAvailableDivisions([]);
    }

    // Reset lower level selections
    setSelectedDivisions([]);
    setSelectedSubDivisions([]);
    setSelectedCategories([]);
    setSelectedGrades([]);
    setSelectedDesignations([]);

    // Notify parent component of changes
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: selectedDepartmentGroups,
      departments: selectedDepartments,
      divisions: [],
      subDivisions: [],
      categories: [],
      grades: [],
      designations: [],
    });
  }, [selectedDepartments, organizationData.divisions, selectedBusinessUnits, selectedDepartmentGroups]);

  // Update available sub-divisions when divisions change
  useEffect(() => {
    if (selectedDivisions.length > 0) {
      const allSubDivisions = selectedDivisions.flatMap(
        div => organizationData.subDivisions[div] || []
      );
      setAvailableSubDivisions([...new Set(allSubDivisions)]);
    } else {
      setAvailableSubDivisions([]);
    }

    // Reset lower level selections
    setSelectedSubDivisions([]);
    setSelectedCategories([]);
    setSelectedGrades([]);
    setSelectedDesignations([]);

    // Notify parent component of changes
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: selectedDepartmentGroups,
      departments: selectedDepartments,
      divisions: selectedDivisions,
      subDivisions: [],
      categories: [],
      grades: [],
      designations: [],
    });
  }, [selectedDivisions, organizationData.subDivisions, selectedBusinessUnits, selectedDepartmentGroups, selectedDepartments]);

  // Update available categories when sub-divisions change
  useEffect(() => {
    if (selectedSubDivisions.length > 0) {
      const allCategories = selectedSubDivisions.flatMap(
        subdiv => organizationData.categories[subdiv] || []
      );
      setAvailableCategories([...new Set(allCategories)]);
    } else {
      setAvailableCategories([]);
    }

    // Reset lower level selections
    setSelectedCategories([]);
    setSelectedGrades([]);
    setSelectedDesignations([]);

    // Notify parent component of changes
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: selectedDepartmentGroups,
      departments: selectedDepartments,
      divisions: selectedDivisions,
      subDivisions: selectedSubDivisions,
      categories: [],
      grades: [],
      designations: [],
    });
  }, [selectedSubDivisions, organizationData.categories, selectedBusinessUnits, selectedDepartmentGroups, selectedDepartments, selectedDivisions]);

  // Update available grades when categories change
  useEffect(() => {
    if (selectedCategories.length > 0) {
      const allGrades = selectedCategories.flatMap(
        cat => organizationData.grades[cat] || []
      );
      setAvailableGrades([...new Set(allGrades)]);
    } else {
      setAvailableGrades([]);
    }

    // Reset lower level selections
    setSelectedGrades([]);
    setSelectedDesignations([]);

    // Notify parent component of changes
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: selectedDepartmentGroups,
      departments: selectedDepartments,
      divisions: selectedDivisions,
      subDivisions: selectedSubDivisions,
      categories: selectedCategories,
      grades: [],
      designations: [],
    });
  }, [selectedCategories, organizationData.grades, selectedBusinessUnits, selectedDepartmentGroups, selectedDepartments, selectedDivisions, selectedSubDivisions]);

  // Update available designations when grades change
  useEffect(() => {
    if (selectedGrades.length > 0) {
      const allDesignations = selectedGrades.flatMap(
        grade => organizationData.designations[grade] || []
      );
      setAvailableDesignations([...new Set(allDesignations)]);
    } else {
      setAvailableDesignations([]);
    }

    // Reset lower level selections
    setSelectedDesignations([]);

    // Notify parent component of changes
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: selectedDepartmentGroups,
      departments: selectedDepartments,
      divisions: selectedDivisions,
      subDivisions: selectedSubDivisions,
      categories: selectedCategories,
      grades: selectedGrades,
      designations: [],
    });
  }, [selectedGrades, organizationData.designations, selectedBusinessUnits, selectedDepartmentGroups, selectedDepartments, selectedDivisions, selectedSubDivisions, selectedCategories]);

  // Notify parent component when designations change
  useEffect(() => {
    onChange({
      businessUnits: selectedBusinessUnits,
      departmentGroups: selectedDepartmentGroups,
      departments: selectedDepartments,
      divisions: selectedDivisions,
      subDivisions: selectedSubDivisions,
      categories: selectedCategories,
      grades: selectedGrades,
      designations: selectedDesignations,
    });
  }, [selectedDesignations, selectedBusinessUnits, selectedDepartmentGroups, selectedDepartments, selectedDivisions, selectedSubDivisions, selectedCategories, selectedGrades]);

  return (
    <div className="space-y-4">
      {/* Top level multi-selects in a grid for larger screens */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Business Units (multi-select) */}
        <div>
          <MultiSelectCheckbox
            title="Business Units"
            options={organizationData.businessUnits}
            selectedValues={selectedBusinessUnits}
            onChange={setSelectedBusinessUnits}
            maxHeight="150px"
          />
        </div>

        {/* Department Groups (multi-select) - Only shown when at least one Business Unit is selected */}
        {selectedBusinessUnits.length > 0 ? (
          <div>
            <MultiSelectCheckbox
              title="Department Groups"
              options={availableDepartmentGroups}
              selectedValues={selectedDepartmentGroups}
              onChange={setSelectedDepartmentGroups}
              maxHeight="150px"
            />
          </div>
        ) : (
          <div className="hidden md:block">
            {/* Empty div to maintain grid layout when department groups aren't shown */}
          </div>
        )}
      </div>

      {/* Multi-select dropdowns in a grid layout */}
      {selectedDepartmentGroups.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {/* Department (multi-select) */}
          <div>
            <MultiSelectCheckbox
              title="Department"
              options={availableDepartments}
              selectedValues={selectedDepartments}
              onChange={setSelectedDepartments}
              maxHeight="150px"
            />
          </div>

          {/* Division (multi-select) - Only shown when at least one Department is selected */}
          {selectedDepartments.length > 0 && (
            <div>
              <MultiSelectCheckbox
                title="Division"
                options={availableDivisions}
                selectedValues={selectedDivisions}
                onChange={setSelectedDivisions}
                maxHeight="150px"
              />
            </div>
          )}
        </div>
      )}

      {/* Additional multi-select dropdowns */}
      {selectedDivisions.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
          {/* Sub-Division (multi-select) */}
          <div>
            <MultiSelectCheckbox
              title="Sub-Division"
              options={availableSubDivisions}
              selectedValues={selectedSubDivisions}
              onChange={setSelectedSubDivisions}
              maxHeight="150px"
            />
          </div>

          {/* Category (multi-select) - Only shown when at least one Sub-Division is selected */}
          {selectedSubDivisions.length > 0 && (
            <div>
              <MultiSelectCheckbox
                title="Category"
                options={availableCategories}
                selectedValues={selectedCategories}
                onChange={setSelectedCategories}
                maxHeight="150px"
              />
            </div>
          )}
        </div>
      )}

      {/* Final level multi-select dropdowns */}
      {selectedCategories.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
          {/* Grade (multi-select) */}
          <div>
            <MultiSelectCheckbox
              title="Grade"
              options={availableGrades}
              selectedValues={selectedGrades}
              onChange={setSelectedGrades}
              maxHeight="150px"
            />
          </div>

          {/* Designation (multi-select) - Only shown when at least one Grade is selected */}
          {selectedGrades.length > 0 && (
            <div>
              <MultiSelectCheckbox
                title="Designation"
                options={availableDesignations}
                selectedValues={selectedDesignations}
                onChange={setSelectedDesignations}
                maxHeight="150px"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
}
