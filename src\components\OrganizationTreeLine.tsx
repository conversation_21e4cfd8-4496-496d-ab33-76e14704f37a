import React from "react";
import { ChevronRight } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface OrganizationTreeLineProps {
  selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  };
}

export function OrganizationTreeLine({ selection }: OrganizationTreeLineProps) {
  // Check if there are any selections
  const hasSelections = 
    selection.businessUnits.length > 0 ||
    selection.departmentGroups.length > 0 ||
    selection.departments.length > 0 ||
    selection.divisions.length > 0 ||
    selection.subDivisions.length > 0 ||
    selection.categories.length > 0 ||
    selection.grades.length > 0 ||
    selection.designations.length > 0;

  if (!hasSelections) {
    return <div className="text-sm text-muted-foreground italic">No selections made</div>;
  }

  // Function to get badge color based on selection type
  const getBadgeClass = (type: string) => {
    switch (type) {
      case 'Business Units':
        return "bg-blue-50 text-blue-700 border-blue-200";
      case 'Department Groups':
        return "bg-purple-50 text-purple-700 border-purple-200";
      case 'Departments':
        return "bg-green-50 text-green-700 border-green-200";
      case 'Divisions':
        return "bg-orange-50 text-orange-700 border-orange-200";
      case 'Sub-Divisions':
        return "bg-red-50 text-red-700 border-red-200";
      case 'Categories':
        return "bg-teal-50 text-teal-700 border-teal-200";
      case 'Grades':
        return "bg-indigo-50 text-indigo-700 border-indigo-200";
      case 'Designations':
        return "bg-pink-50 text-pink-700 border-pink-200";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200";
    }
  };

  // Create a structured representation of the hierarchy
  const renderHierarchyLines = () => {
    const lines = [];

    // For each business unit
    for (const bu of selection.businessUnits) {
      // Create a line for this business unit
      const line = (
        <div key={bu} className="mb-3 border-b pb-3 last:border-0 last:pb-0">
          <div className="text-xs text-muted-foreground mb-1">Business Unit:</div>
          <div className="flex items-center flex-wrap gap-1">
            <Badge variant="outline" className={getBadgeClass('Business Units')}>
              {bu}
            </Badge>

            {selection.departmentGroups.length > 0 && (
              <>
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                <div className="flex flex-wrap gap-1 items-center">
                  {selection.departmentGroups.map((dg, index) => (
                    <Badge key={dg} variant="outline" className={getBadgeClass('Department Groups')}>
                      {dg}
                    </Badge>
                  ))}
                </div>
              </>
            )}

            {selection.departments.length > 0 && (
              <>
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                <div className="flex flex-wrap gap-1 items-center">
                  {selection.departments.map((dept) => (
                    <Badge key={dept} variant="outline" className={getBadgeClass('Departments')}>
                      {dept}
                    </Badge>
                  ))}
                </div>
              </>
            )}

            {selection.divisions.length > 0 && (
              <>
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                <div className="flex flex-wrap gap-1 items-center">
                  {selection.divisions.map((div) => (
                    <Badge key={div} variant="outline" className={getBadgeClass('Divisions')}>
                      {div}
                    </Badge>
                  ))}
                </div>
              </>
            )}

            {selection.subDivisions.length > 0 && (
              <>
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                <div className="flex flex-wrap gap-1 items-center">
                  {selection.subDivisions.map((subdiv) => (
                    <Badge key={subdiv} variant="outline" className={getBadgeClass('Sub-Divisions')}>
                      {subdiv}
                    </Badge>
                  ))}
                </div>
              </>
            )}

            {selection.categories.length > 0 && (
              <>
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                <div className="flex flex-wrap gap-1 items-center">
                  {selection.categories.map((cat) => (
                    <Badge key={cat} variant="outline" className={getBadgeClass('Categories')}>
                      {cat}
                    </Badge>
                  ))}
                </div>
              </>
            )}

            {selection.grades.length > 0 && (
              <>
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                <div className="flex flex-wrap gap-1 items-center">
                  {selection.grades.map((grade) => (
                    <Badge key={grade} variant="outline" className={getBadgeClass('Grades')}>
                      {grade}
                    </Badge>
                  ))}
                </div>
              </>
            )}

            {selection.designations.length > 0 && (
              <>
                <ChevronRight className="h-4 w-4 text-muted-foreground mx-1" />
                <div className="flex flex-wrap gap-1 items-center">
                  {selection.designations.map((desig) => (
                    <Badge key={desig} variant="outline" className={getBadgeClass('Designations')}>
                      {desig}
                    </Badge>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      );

      lines.push(line);
    }

    return lines;
  };

  // If there are no business units selected, show a simplified view
  if (selection.businessUnits.length === 0) {
    return (
      <div className="space-y-2">
        <div className="text-sm font-medium">Selection Summary:</div>
        <div className="flex flex-wrap gap-2">
          {selection.departmentGroups.map(group => (
            <Badge key={group} variant="outline" className={getBadgeClass('Department Groups')}>
              {group}
            </Badge>
          ))}
          {selection.departments.map(dept => (
            <Badge key={dept} variant="outline" className={getBadgeClass('Departments')}>
              {dept}
            </Badge>
          ))}
          {selection.divisions.map(div => (
            <Badge key={div} variant="outline" className={getBadgeClass('Divisions')}>
              {div}
            </Badge>
          ))}
          {selection.subDivisions.map(subdiv => (
            <Badge key={subdiv} variant="outline" className={getBadgeClass('Sub-Divisions')}>
              {subdiv}
            </Badge>
          ))}
          {selection.categories.map(cat => (
            <Badge key={cat} variant="outline" className={getBadgeClass('Categories')}>
              {cat}
            </Badge>
          ))}
          {selection.grades.map(grade => (
            <Badge key={grade} variant="outline" className={getBadgeClass('Grades')}>
              {grade}
            </Badge>
          ))}
          {selection.designations.map(designation => (
            <Badge key={designation} variant="outline" className={getBadgeClass('Designations')}>
              {designation}
            </Badge>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-2">
      <div className="text-sm font-medium">Selection Summary</div>
      <div className="border rounded-md p-3 bg-muted/5">
        {renderHierarchyLines()}
      </div>
    </div>
  );
}
