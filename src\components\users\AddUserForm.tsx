
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const UserSchema = z.object({
  name: z.string().min(2, { message: "Name is required" }),
  employeeNo: z.string().min(1, { message: "Employee number is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  phone: z.string().min(1, { message: "Phone number is required" }),
  dateOfJoining: z.date({ required_error: "Date of joining is required" }),
  division: z.string().min(1, { message: "Division is required" }),
  department: z.string().min(1, { message: "Department is required" }),
  location: z.string().min(1, { message: "Location is required" }),
});

type UserFormData = z.infer<typeof UserSchema>;

// Location, Division, and Department data from the attachment
const locationOptions = ["KHS", "KCN", "KTN"];

const divisionOptions = [
  "Clinical Support",
  "Facility Services",
  "Operations",
  "Clinical Nursing",
  "Clinical Research"
];

const departmentOptions = [
  "Biomedical",
  "Engineering",
  "Facility Services",
  "OPD",
  "Nursing",
  "Critical Care"
];

// Map of division to departments for dependent dropdown
const divisionToDepartments = {
  "Clinical Support": ["Biomedical", "Critical Care", "Pharmacy", "Business Excellence"],
  "Facility Services": ["Engineering", "Facility Services"],
  "Operations": ["OPD", "Clinical Pharmacology", "Central Support Team"],
  "Clinical Nursing": ["OPD", "Nursing", "Transplant"],
  "Clinical Research": ["Clinical Research"]
};

interface AddUserFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddUser: (data: UserFormData) => void;
  divisions: string[];
  departments: string[];
  locations: string[];
  userType: "internal" | "external";
}

const AddUserForm: React.FC<AddUserFormProps> = ({
  open,
  onOpenChange,
  onAddUser,
  divisions,
  departments,
  locations,
  userType,
}) => {
  // State for filtered departments based on selected division
  const [filteredDepartments, setFilteredDepartments] = useState<string[]>([]);
  const { toast } = useToast();

  const form = useForm<UserFormData>({
    resolver: zodResolver(UserSchema),
    defaultValues: {
      name: "",
      employeeNo: "",
      email: "",
      phone: "",
      division: "",
      department: "",
      location: "",
    },
  });

  // Update departments when division changes
  const handleDivisionChange = (value: string) => {
    form.setValue("division", value);
    form.setValue("department", ""); // Reset department when division changes

    // Update filtered departments based on selected division
    if (value && divisionToDepartments[value]) {
      setFilteredDepartments(divisionToDepartments[value]);
    } else {
      setFilteredDepartments(departmentOptions);
    }
  };

  // Initialize filtered departments
  useEffect(() => {
    const currentDivision = form.getValues("division");
    if (currentDivision && divisionToDepartments[currentDivision]) {
      setFilteredDepartments(divisionToDepartments[currentDivision]);
    } else {
      setFilteredDepartments(departmentOptions);
    }
  }, [form]);

  const onSubmit = (data: UserFormData) => {
    onAddUser(data);
    form.reset();
    onOpenChange(false);
    toast({
      title: "User Added",
      description: `${userType === "internal" ? "Internal" : "External"} user ${data.name} has been added successfully.`,
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[550px] p-6">
        <DialogHeader>
          <DialogTitle>
            Add {userType === "internal" ? "Internal" : "External"} User
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="pt-2">
            <div className="space-y-5">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="employeeNo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        {userType === "internal" ? "Employee No." : "External ID"}
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Enter ID number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter email" type="email" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter phone number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="dateOfJoining"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Date of Joining</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            initialFocus
                            className={cn("p-3 pointer-events-auto")}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="division"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Division</FormLabel>
                        <Select onValueChange={handleDivisionChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select division" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {divisionOptions.map((division) => (
                              <SelectItem key={division} value={division}>
                                {division}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Department</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select department" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {filteredDepartments.map((department) => (
                              <SelectItem key={department} value={department}>
                                {department}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select location" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {locationOptions.map((location) => (
                              <SelectItem key={location} value={location}>
                                {location}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
            </div>

            <DialogFooter className="pt-6 space-x-2">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" className="bg-primary">Add User</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddUserForm;
