import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SelectOrganizationDialog, SelectionSummary } from "@/components/SelectOrganizationDialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function OrganizationSelectorDemo() {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selection, setSelection] = useState({
    businessUnits: ["KCN", "KTN"],
    departmentGroups: ["Clinical Services", "Emergency Services"],
    departments: ["Surgery"],
    divisions: ["Orthopedic Surgery"],
    subDivisions: ["Spine Surgery"],
    categories: ["Thoracic Spine"],
    grades: [],
  });

  return (
    <div className="container py-10">
      <h1 className="text-3xl font-bold mb-8">Organization Selector Demo</h1>

      <Tabs defaultValue="broadcast" className="mb-8">
        <TabsList>
          <TabsTrigger value="broadcast">Broadcast</TabsTrigger>
          <TabsTrigger value="organization">Organization</TabsTrigger>
        </TabsList>
        <TabsContent value="broadcast">
          <Card>
            <CardHeader>
              <CardTitle>Broadcast Message</CardTitle>
              <CardDescription>
                Send a broadcast message to selected organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">Target Organization</h3>
                <Button
                  onClick={() => setDialogOpen(true)}
                  variant="outline"
                  className="w-full justify-start h-auto py-3 px-4"
                >
                  <div className="text-left">
                    <p className="text-sm font-medium">Select Organization</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Click to select organizations to target your broadcast
                    </p>
                  </div>
                </Button>
              </div>

              {(selection.businessUnits.length > 0 ||
                selection.departmentGroups.length > 0 ||
                selection.departments.length > 0) && (
                <SelectionSummary
                  selection={selection}
                  onEdit={() => setDialogOpen(true)}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="organization">
          <Card>
            <CardHeader>
              <CardTitle>Organization Selection</CardTitle>
              <CardDescription>
                Select organizations from the hierarchical structure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => setDialogOpen(true)}
                className="mb-6"
              >
                Select Organization
              </Button>

              <SelectionSummary
                selection={selection}
                onEdit={() => setDialogOpen(true)}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <SelectOrganizationDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onConfirm={setSelection}
        initialSelection={selection}
      />
    </div>
  );
}
