import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Legend
} from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from 'framer-motion';

interface AnimatedPieChartProps {
  title: string;
  data: any[];
  dataKey: string;
  nameKey: string;
  height?: number;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
}

const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index, name }: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      fontSize={12}
      fontWeight="bold"
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const AnimatedPieChart: React.FC<AnimatedPieChartProps> = ({
  title,
  data,
  dataKey,
  nameKey,
  height = 300,
  showLegend = true,
  innerRadius = 60,
  outerRadius = 100
}) => {
  const [chartData, setChartData] = useState<any[]>([]);
  
  // Animate the data on mount
  useEffect(() => {
    // Start with equal values
    const total = data.reduce((sum, item) => sum + item[dataKey], 0);
    const equalValue = total / data.length;
    
    const initialData = data.map(item => ({
      ...item,
      [dataKey]: equalValue
    }));
    
    setChartData(initialData);
    
    // Animate to actual values
    const timer = setTimeout(() => {
      setChartData(data);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [data, dataKey]);
  
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="overflow-hidden">
        <CardHeader className="pb-2">
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <CardTitle>{title}</CardTitle>
          </motion.div>
        </CardHeader>
        <CardContent className={`h-[${height}px]`}>
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={outerRadius}
                innerRadius={innerRadius}
                fill="#8884d8"
                dataKey={dataKey}
                nameKey={nameKey}
                animationDuration={1500}
                animationEasing="ease-in-out"
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color || `hsl(${index * 45}, 70%, 60%)`} 
                  />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value) => `${value}`}
                contentStyle={{ 
                  borderRadius: '8px', 
                  border: 'none', 
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  padding: '12px'
                }}
              />
              {showLegend && (
                <Legend 
                  layout="horizontal" 
                  verticalAlign="bottom" 
                  align="center"
                  wrapperStyle={{ paddingTop: '20px' }}
                />
              )}
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default AnimatedPieChart;
