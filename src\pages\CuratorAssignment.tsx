
import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { UserPlus, X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";

// Mock data
const knowledgeAreas = [
  { id: "1", name: "Computer Science" },
  { id: "2", name: "Business Management" },
  { id: "3", name: "Data Science" },
];

const users = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Engineering",
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Finance",
  },
  {
    id: "3",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Product",
  },
  {
    id: "4",
    name: "Morgan Williams",
    email: "<EMAIL>",
    department: "Customer Support",
  },
  {
    id: "5",
    name: "<PERSON>",
    email: "<EMAIL>",
    department: "Marketing",
  },
];

// Initial assignments
const initialAssignments = {
  "1": ["1", "3"], // Computer Science area has users 1 and 3 assigned
  "2": ["2"], // Business Management area has user 2 assigned
  "3": ["5"], // Data Science area has user 5 assigned
};

export default function CuratorAssignment() {
  const [selectedArea, setSelectedArea] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [assignments, setAssignments] = useState(initialAssignments);

  const handleAssignCurator = (userId: string) => {
    if (!selectedArea) return;

    const currentAssignments = assignments[selectedArea] || [];
    if (currentAssignments.includes(userId)) {
      toast({
        title: "Already assigned",
        description: "This user is already assigned as a curator for this area.",
        variant: "destructive",
      });
      return;
    }

    setAssignments({
      ...assignments,
      [selectedArea]: [...currentAssignments, userId],
    });

    setDialogOpen(false);
    
    const user = users.find(u => u.id === userId);
    const area = knowledgeAreas.find(a => a.id === selectedArea);
    
    toast({
      title: "Curator assigned",
      description: `${user?.name} has been assigned as a curator for ${area?.name}.`,
    });
  };

  const handleRemoveCurator = (userId: string) => {
    if (!selectedArea) return;

    const currentAssignments = assignments[selectedArea] || [];
    setAssignments({
      ...assignments,
      [selectedArea]: currentAssignments.filter((id) => id !== userId),
    });

    const user = users.find(u => u.id === userId);
    const area = knowledgeAreas.find(a => a.id === selectedArea);
    
    toast({
      title: "Curator removed",
      description: `${user?.name} has been removed as a curator from ${area?.name}.`,
    });
  };

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const assignedUsers = selectedArea
    ? users.filter((user) => (assignments[selectedArea] || []).includes(user.id))
    : [];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">
          Curator Assignment
        </h1>
      </div>

      <Card className="p-6">
        <div className="space-y-6">
          <div className="max-w-md">
            <label className="text-sm font-medium">Select Knowledge Area</label>
            <Select
              value={selectedArea}
              onValueChange={(value) => setSelectedArea(value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select an area" />
              </SelectTrigger>
              <SelectContent>
                {knowledgeAreas.map((area) => (
                  <SelectItem key={area.id} value={area.id}>
                    {area.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedArea && (
            <>
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">
                  Assigned Curators for{" "}
                  {knowledgeAreas.find((a) => a.id === selectedArea)?.name}
                </h2>
                <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                  <DialogTrigger asChild>
                    <Button className="gap-2">
                      <UserPlus size={16} />
                      Assign Curator
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Assign Curator</DialogTitle>
                      <DialogDescription>
                        Select a user to assign as curator for{" "}
                        {
                          knowledgeAreas.find((a) => a.id === selectedArea)?.name
                        }
                        .
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <Input
                        placeholder="Search users..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="mb-4"
                      />
                      <div className="max-h-60 overflow-y-auto border rounded-md">
                        <Table>
                          <TableBody>
                            {filteredUsers.map((user) => (
                              <TableRow key={user.id}>
                                <TableCell>
                                  <div>
                                    <div className="font-medium">
                                      {user.name}
                                    </div>
                                    <div className="text-sm text-muted-foreground">
                                      {user.email}
                                    </div>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge>{user.department}</Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    size="sm"
                                    onClick={() => handleAssignCurator(user.id)}
                                  >
                                    Assign
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                            {filteredUsers.length === 0 && (
                              <TableRow>
                                <TableCell colSpan={3} className="text-center py-4">
                                  No users found
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setDialogOpen(false)}>
                        Cancel
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>

              <div className="border rounded-md overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {assignedUsers.length > 0 ? (
                      assignedUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell className="font-medium">
                            {user.name}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>{user.department}</TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleRemoveCurator(user.id)}
                              className="text-red-500 hover:text-red-600"
                            >
                              <X size={16} />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell
                          colSpan={4}
                          className="text-center py-6 text-muted-foreground"
                        >
                          No curators assigned to this area yet
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </>
          )}

          {!selectedArea && (
            <div className="text-center py-12 text-muted-foreground">
              Please select a knowledge area to manage curator assignments
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
