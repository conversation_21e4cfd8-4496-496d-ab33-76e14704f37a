import React from "react";
import { ChevronRight } from "lucide-react";

interface SelectedUnitsWithHierarchyProps {
  selectedUnits: string[];
  areas: string[];
  topics: Record<string, string[]>;
  units: Record<string, string[]>;
  assignmentType?: "Training" | "CFD";
  selectedTopics?: {area: string, topic: string}[];
  selectedCfdUnits?: {area: string, topic: string, unit: string}[];
  showFullPath?: boolean;
}

interface UnitWithHierarchy {
  area: string;
  topic: string;
  unit: string;
}

const SelectedUnitsWithHierarchy: React.FC<SelectedUnitsWithHierarchyProps> = ({
  selectedUnits,
  areas,
  topics,
  units,
  assignmentType = "Training",
  selectedTopics = [],
  selectedCfdUnits = [],
  showFullPath = false,
}) => {
  // For Training, use selectedTopics directly
  if (assignmentType === "Training" && selectedTopics.length > 0) {
    // Group topics by area
    const groupedTopics: Record<string, string[]> = {};

    selectedTopics.forEach(({ area, topic }) => {
      if (!groupedTopics[area]) {
        groupedTopics[area] = [];
      }

      if (!groupedTopics[area].includes(topic)) {
        groupedTopics[area].push(topic);
      }
    });

    // Convert to the same format as groupedUnits
    const groupedUnits: Record<string, Record<string, string[]>> = {};

    Object.entries(groupedTopics).forEach(([area, topicList]) => {
      groupedUnits[area] = {};

      topicList.forEach(topic => {
        // For each topic, get its units
        const topicUnits = units[topic] || [];
        groupedUnits[area][topic] = topicUnits;
      });
    });

    if (selectedTopics.length === 0) {
      return (
        <div className="text-sm text-muted-foreground italic">
          No topics selected
        </div>
      );
    }

    // If showFullPath is true, display in the format shown in the image
    if (showFullPath) {
      return (
        <div className="space-y-4">
          {Object.entries(groupedUnits).map(([area, topicUnits]) => (
            <div key={area}>
              {Object.entries(topicUnits).map(([topic, unitList]) => (
                <div key={`${area}-${topic}`} className="border rounded-md overflow-hidden">
                  <div className="p-3 bg-white">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {area}
                      </span>
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {topic}
                      </span>
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50">
                    {unitList.map((unit) => (
                      <div key={unit} className="text-sm text-gray-700 py-1 flex items-start">
                        <span className="mr-2 text-primary">•</span>
                        <span>{unit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      );
    }

    // Original display format
    return (
      <div className="space-y-4">
        {Object.entries(groupedUnits).map(([area, topicUnits]) => (
          <div key={area} className="border rounded-md overflow-hidden">
            {Object.entries(topicUnits).map(([topic, unitList]) => (
              <div key={`${area}-${topic}`}>
                <div className="p-3 bg-white border-b">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-foreground">
                      {area}
                    </span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium text-foreground">
                      {topic}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-gray-50">
                  <div className="pl-1">
                    {unitList.map((unit) => (
                      <div key={unit} className="text-sm text-gray-700 py-1 flex items-start">
                        <span className="mr-2 text-primary">•</span>
                        <span>{unit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  }

  // For CFD, use selectedCfdUnits directly
  if (assignmentType === "CFD" && selectedCfdUnits.length > 0) {
    // Group units by area and topic
    const groupedUnits: Record<string, Record<string, string[]>> = {};

    selectedCfdUnits.forEach(({ area, topic, unit }) => {
      if (!groupedUnits[area]) {
        groupedUnits[area] = {};
      }

      if (!groupedUnits[area][topic]) {
        groupedUnits[area][topic] = [];
      }

      groupedUnits[area][topic].push(unit);
    });

    if (selectedCfdUnits.length === 0) {
      return (
        <div className="text-sm text-muted-foreground italic">
          No documents selected
        </div>
      );
    }

    // If showFullPath is true, display in the format shown in the image
    if (showFullPath) {
      return (
        <div className="space-y-4">
          {Object.entries(groupedUnits).map(([area, topicUnits]) => (
            <div key={area}>
              {Object.entries(topicUnits).map(([topic, unitList]) => (
                <div key={`${area}-${topic}`} className="border rounded-md overflow-hidden">
                  <div className="p-3 bg-white">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">
                        {area}
                      </span>
                      <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">
                        {topic}
                      </span>
                    </div>
                  </div>
                  <div className="p-3 bg-gray-50">
                    {unitList.map((unit) => (
                      <div key={unit} className="text-sm text-gray-700 py-1 flex items-start">
                        <span className="mr-2 text-primary">•</span>
                        <span>{unit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      );
    }

    // Original display format
    return (
      <div className="space-y-4">
        {Object.entries(groupedUnits).map(([area, topicUnits]) => (
          <div key={area} className="border rounded-md overflow-hidden">
            {Object.entries(topicUnits).map(([topic, unitList]) => (
              <div key={`${area}-${topic}`}>
                <div className="p-3 bg-white border-b">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-foreground">
                      {area}
                    </span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium text-foreground">
                      {topic}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-gray-50">
                  <div className="pl-1">
                    {unitList.map((unit) => (
                      <div key={unit} className="text-sm text-gray-700 py-1 flex items-start">
                        <span className="mr-2 text-primary">•</span>
                        <span>{unit}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  }

  // Fallback to the original logic if no selectedTopics or selectedCfdUnits provided
  // Find the area and topic for each selected unit
  const unitsWithHierarchy: UnitWithHierarchy[] = selectedUnits.map((unit) => {
    let foundArea = "";
    let foundTopic = "";

    // Find the topic that contains this unit
    for (const topic in units) {
      if (units[topic].includes(unit)) {
        foundTopic = topic;
        break;
      }
    }

    // Find the area that contains this topic
    if (foundTopic) {
      for (const area in topics) {
        if (topics[area].includes(foundTopic)) {
          foundArea = area;
          break;
        }
      }
    }

    return {
      area: foundArea,
      topic: foundTopic,
      unit,
    };
  });

  // Group units by area and topic
  const groupedUnits: Record<string, Record<string, string[]>> = {};

  unitsWithHierarchy.forEach(({ area, topic, unit }) => {
    if (!groupedUnits[area]) {
      groupedUnits[area] = {};
    }

    if (!groupedUnits[area][topic]) {
      groupedUnits[area][topic] = [];
    }

    groupedUnits[area][topic].push(unit);
  });

  if (selectedUnits.length === 0) {
    return (
      <div className="text-sm text-muted-foreground italic">
        {assignmentType === "Training" ? "No topics selected" : "No documents selected"}
      </div>
    );
  }

  // If showFullPath is true, display in the format shown in the image
  if (showFullPath) {
    return (
      <div className="space-y-4">
        {Object.entries(groupedUnits).map(([area, topicUnits]) => (
          <div key={area}>
            {Object.entries(topicUnits).map(([topic, unitList]) => (
              <div key={`${area}-${topic}`} className="border rounded-md overflow-hidden">
                <div className="p-3 bg-white">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {area}
                    </span>
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {topic}
                    </span>
                  </div>
                </div>
                <div className="p-3 bg-gray-50">
                  {unitList.map((unit) => (
                    <div key={unit} className="text-sm text-gray-700 py-1 flex items-start">
                      <span className="mr-2 text-primary">•</span>
                      <span>{unit}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  }

  // Original display format
  return (
    <div className="space-y-4">
      {Object.entries(groupedUnits).map(([area, topicUnits]) => (
        <div key={area} className="border rounded-md overflow-hidden">
          {Object.entries(topicUnits).map(([topic, unitList]) => (
            <div key={`${area}-${topic}`}>
              <div className="p-3 bg-white border-b">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-foreground">
                    {area}
                  </span>
                  <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-foreground">
                    {topic}
                  </span>
                </div>
              </div>
              <div className="p-3 bg-gray-50">
                {assignmentType === "Training" ? (
                  // For Training, show only the units as bullet points
                  <div className="pl-1">
                    {unitList.map((unit) => (
                      <div key={unit} className="text-sm text-gray-700 py-1 flex items-start">
                        <span className="mr-2 text-primary">•</span>
                        <span>{unit}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  // For CFD, show each unit with its area and topic
                  <div className="flex flex-wrap gap-2">
                    {unitList.map((unit) => (
                      <span
                        key={unit}
                        className="text-sm font-medium bg-gray-100 text-gray-700 px-3 py-1 rounded-full"
                      >
                        {unit}
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};

export default SelectedUnitsWithHierarchy;
