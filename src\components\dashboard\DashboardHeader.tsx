import React from 'react';
import { Button } from "@/components/ui/button";
import { Calendar, Download, Filter, RefreshCw, ChevronDown } from "lucide-react";
import { motion } from "framer-motion";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DashboardHeaderProps {
  title: string;
  subtitle?: string;
  onRefresh?: () => void;
  onExport?: () => void;
  onFilterChange?: (period: string) => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  subtitle,
  onRefresh,
  onExport,
  onFilterChange
}) => {
  const [selectedPeriod, setSelectedPeriod] = React.useState("Last 30 Days");
  
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    if (onFilterChange) {
      onFilterChange(period);
    }
  };
  
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0, y: -20 },
    show: { opacity: 1, y: 0 }
  };
  
  return (
    <motion.div 
      className="flex flex-col space-y-2 mb-8"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <div className="flex items-center justify-between">
        <motion.div variants={item}>
          <h1 className="text-3xl font-bold tracking-tight">{title}</h1>
          {subtitle && <p className="text-muted-foreground mt-1">{subtitle}</p>}
        </motion.div>
        
        <div className="flex items-center gap-3">
          <motion.div variants={item}>
            <Button 
              variant="outline" 
              size="sm" 
              className="gap-1"
              onClick={onRefresh}
            >
              <RefreshCw size={16} className="mr-1" /> Refresh
            </Button>
          </motion.div>
          
          <motion.div variants={item}>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="gap-1">
                  <Filter size={16} /> Filter <ChevronDown size={14} />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Time Period</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handlePeriodChange("Today")}>
                  Today
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodChange("Last 7 Days")}>
                  Last 7 Days
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodChange("Last 30 Days")}>
                  Last 30 Days
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodChange("This Month")}>
                  This Month
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodChange("This Quarter")}>
                  This Quarter
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handlePeriodChange("This Year")}>
                  This Year
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </motion.div>
          
          <motion.div variants={item}>
            <Button variant="outline" size="sm" className="gap-1" onClick={onExport}>
              <Calendar size={16} /> {selectedPeriod}
            </Button>
          </motion.div>
          
          <motion.div 
            variants={item}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button variant="default" size="sm" className="gap-1" onClick={onExport}>
              <Download size={16} /> Export
            </Button>
          </motion.div>
        </div>
      </div>
      
      <motion.div 
        className="h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full"
        initial={{ width: 0, opacity: 0 }}
        animate={{ width: "100%", opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.8 }}
      />
    </motion.div>
  );
};

export default DashboardHeader;
