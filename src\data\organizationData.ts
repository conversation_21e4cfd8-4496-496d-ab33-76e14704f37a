// Mock data for organizational hierarchy

// Business Units
export const businessUnits = ["KCN", "KTN", "KHC", "KVP", "KRR", "KHO", "KHS"];

// Department Groups by Business Unit
export const departmentGroups: Record<string, string[]> = {
  "KCN": ["Clinical Services", "Emergency Services", "Management", "Quality Assurance"],
  "KTN": ["Information Management", "Infection Prevention", "IT Services", "Patient Services"],
  "KHC": ["Ethics Committee", "Pharmacy Services", "Nursing Services", "Diagnostic Services"],
  "KVP": ["Administrative Services", "Support Services", "Maintenance", "Security"],
  "KRR": ["Research", "Education", "Training", "Development"],
  "KHO": ["Outpatient Services", "Inpatient Services", "Specialty Services", "Rehabilitation"],
  "KHS": ["Human Resources", "Finance", "Operations", "Marketing"]
};

// Departments by Department Group
export const departments: Record<string, string[]> = {
  "Clinical Services": ["General Medicine", "Surgery", "Pediatrics", "Obstetrics & Gynecology"],
  "Emergency Services": ["Trauma", "Critical Care", "Ambulatory", "Triage"],
  "Management": ["Hospital Administration", "Clinical Management", "Operations Management", "Strategic Planning"],
  "Quality Assurance": ["Quality Control", "Compliance", "Risk Management", "Patient Safety"],
  "Information Management": ["Health Records", "Data Analytics", "Reporting", "Documentation"],
  "Infection Prevention": ["Infection Control", "Sterilization", "Hygiene Monitoring", "Outbreak Management"],
  "IT Services": ["Technical Support", "Systems Administration", "Network Management", "Application Development"],
  "Patient Services": ["Patient Relations", "Admission", "Discharge Planning", "Patient Experience"],
  "Ethics Committee": ["Clinical Ethics", "Research Ethics", "Ethical Review", "Policy Development"],
  "Pharmacy Services": ["Inpatient Pharmacy", "Outpatient Pharmacy", "Clinical Pharmacy", "Pharmacy Informatics"],
  "Nursing Services": ["Medical Nursing", "Surgical Nursing", "Pediatric Nursing", "Geriatric Nursing"],
  "Diagnostic Services": ["Radiology", "Laboratory", "Pathology", "Imaging"],
  "Administrative Services": ["Front Office", "Back Office", "Scheduling", "Reception"],
  "Support Services": ["Housekeeping", "Food Services", "Laundry", "Transportation"],
  "Maintenance": ["Facility Maintenance", "Biomedical Engineering", "Equipment Maintenance", "Grounds"],
  "Security": ["Access Control", "Surveillance", "Emergency Response", "Safety"],
  "Research": ["Clinical Research", "Medical Research", "Health Systems Research", "Innovation"],
  "Education": ["Medical Education", "Nursing Education", "Allied Health Education", "Continuing Education"],
  "Training": ["Staff Training", "Professional Development", "Certification", "Orientation"],
  "Development": ["Program Development", "Curriculum Development", "Instructional Design", "Assessment"],
  "Outpatient Services": ["Clinics", "Day Surgery", "Ambulatory Care", "Consultation"],
  "Inpatient Services": ["Ward Management", "Bed Allocation", "Patient Care", "Monitoring"],
  "Specialty Services": ["Cardiology", "Neurology", "Oncology", "Orthopedics"],
  "Rehabilitation": ["Physical Therapy", "Occupational Therapy", "Speech Therapy", "Rehabilitation Medicine"],
  "Human Resources": ["Recruitment", "Employee Relations", "Benefits", "Training & Development"],
  "Finance": ["Accounting", "Billing", "Payroll", "Financial Planning"],
  "Operations": ["Supply Chain", "Logistics", "Procurement", "Inventory Management"],
  "Marketing": ["Communications", "Public Relations", "Digital Marketing", "Community Outreach"]
};

// Divisions by Department
export const divisions: Record<string, string[]> = {
  "General Medicine": ["Internal Medicine", "Family Medicine", "Geriatrics", "Preventive Medicine"],
  "Surgery": ["General Surgery", "Orthopedic Surgery", "Neurosurgery", "Cardiac Surgery"],
  "Pediatrics": ["General Pediatrics", "Neonatal", "Pediatric Specialties", "Adolescent Medicine"],
  "Obstetrics & Gynecology": ["Obstetrics", "Gynecology", "Maternal-Fetal Medicine", "Reproductive Endocrinology"],
  "Trauma": ["Trauma Assessment", "Trauma Surgery", "Trauma ICU", "Trauma Rehabilitation"],
  "Critical Care": ["Medical ICU", "Surgical ICU", "Cardiac ICU", "Neurological ICU"],
  "Technical Support": ["Desktop Support", "Help Desk", "User Training", "Technical Troubleshooting"],
  "Systems Administration": ["Server Management", "Database Administration", "System Configuration", "System Monitoring"],
  "Network Management": ["Network Infrastructure", "Network Security", "Wireless Networks", "Telecommunications"],
  "Application Development": ["Software Development", "Web Development", "Mobile Development", "Integration Services"],
  "Inpatient Pharmacy": ["Medication Distribution", "IV Admixture", "Clinical Monitoring", "Medication Reconciliation"],
  "Outpatient Pharmacy": ["Prescription Filling", "Patient Counseling", "Medication Therapy Management", "Refill Services"],
  "Clinical Pharmacy": ["Medication Review", "Therapeutic Drug Monitoring", "Pharmacokinetics", "Drug Information"],
  "Pharmacy Informatics": ["Pharmacy Systems", "Medication Safety Technology", "Automation", "Analytics"],
  // Add more as needed for other departments
};

// Sub-Divisions by Division
export const subDivisions: Record<string, string[]> = {
  "Internal Medicine": ["Cardiology", "Gastroenterology", "Pulmonology", "Endocrinology"],
  "Family Medicine": ["Primary Care", "Preventive Care", "Chronic Disease Management", "Community Health"],
  "General Surgery": ["Abdominal Surgery", "Breast Surgery", "Colorectal Surgery", "Vascular Surgery"],
  "Orthopedic Surgery": ["Joint Replacement", "Spine Surgery", "Sports Medicine", "Trauma Orthopedics"],
  "Desktop Support": ["Hardware Support", "Software Support", "Peripheral Support", "Remote Support"],
  "Help Desk": ["Tier 1 Support", "Tier 2 Support", "Ticket Management", "Knowledge Base"],
  "Server Management": ["Windows Servers", "Linux Servers", "Virtual Servers", "Cloud Infrastructure"],
  "Database Administration": ["SQL Databases", "NoSQL Databases", "Database Security", "Performance Tuning"],
  "Medication Distribution": ["Unit Dose", "Automated Dispensing", "Controlled Substances", "Emergency Medications"],
  "IV Admixture": ["Chemotherapy", "Nutrition", "Antibiotics", "Pediatric Preparations"],
  // Add more as needed for other divisions
};

// Categories by Sub-Division
export const categories: Record<string, string[]> = {
  "Cardiology": ["Interventional Cardiology", "Non-invasive Cardiology", "Electrophysiology", "Heart Failure"],
  "Gastroenterology": ["Hepatology", "Inflammatory Bowel Disease", "Endoscopy", "Motility"],
  "Joint Replacement": ["Hip Replacement", "Knee Replacement", "Shoulder Replacement", "Revision Surgery"],
  "Spine Surgery": ["Cervical Spine", "Thoracic Spine", "Lumbar Spine", "Minimally Invasive Spine"],
  "Hardware Support": ["Desktop", "Laptop", "Printer", "Scanner"],
  "Software Support": ["Operating System", "Office Applications", "Clinical Applications", "Custom Applications"],
  "Windows Servers": ["Active Directory", "Exchange", "SharePoint", "SQL Server"],
  "Linux Servers": ["Web Servers", "Database Servers", "Application Servers", "File Servers"],
  "Unit Dose": ["Oral Medications", "Injectable Medications", "Topical Medications", "Respiratory Medications"],
  "Automated Dispensing": ["Cabinet Management", "Inventory Control", "User Access", "Override Monitoring"],
  // Add more as needed for other sub-divisions
};

// Grades by Category
export const grades: Record<string, string[]> = {
  "Interventional Cardiology": ["Junior", "Mid-level", "Senior", "Lead"],
  "Non-invasive Cardiology": ["Junior", "Mid-level", "Senior", "Lead"],
  "Hip Replacement": ["Junior", "Mid-level", "Senior", "Lead"],
  "Knee Replacement": ["Junior", "Mid-level", "Senior", "Lead"],
  "Desktop": ["Entry-level", "Associate", "Specialist", "Manager"],
  "Laptop": ["Entry-level", "Associate", "Specialist", "Manager"],
  "Operating System": ["Entry-level", "Associate", "Specialist", "Manager"],
  "Office Applications": ["Entry-level", "Associate", "Specialist", "Manager"],
  "Active Directory": ["Entry-level", "Associate", "Specialist", "Manager"],
  "Exchange": ["Entry-level", "Associate", "Specialist", "Manager"],
  "Oral Medications": ["Junior", "Mid-level", "Senior", "Lead"],
  "Injectable Medications": ["Junior", "Mid-level", "Senior", "Lead"],
  // Add more as needed for other categories
};

// Designations by Grade
export const designations: Record<string, string[]> = {
  "Junior": ["Nurse Assistant", "Junior Technician", "Resident", "Junior Pharmacist"],
  "Mid-level": ["Registered Nurse", "Technician", "Physician", "Staff Pharmacist"],
  "Senior": ["Head Nurse", "Senior Technician", "Attending Physician", "Senior Pharmacist"],
  "Lead": ["Nursing Director", "Technical Lead", "Chief Physician", "Pharmacy Director"],
  "Entry-level": ["Administrative Assistant", "Records Clerk", "Support Staff", "IT Assistant"],
  "Associate": ["Administrative Coordinator", "Records Specialist", "Department Coordinator", "IT Associate"],
  "Specialist": ["IT Specialist", "Systems Specialist", "Technical Specialist", "Application Specialist"],
  "Manager": ["Department Manager", "Administrative Manager", "Operations Manager", "IT Manager"],
  // Add more as needed for other grades
};

// Complete organizational hierarchy
export const organizationHierarchy = {
  businessUnits,
  departmentGroups,
  departments,
  divisions,
  subDivisions,
  categories,
  grades,
  designations
};
