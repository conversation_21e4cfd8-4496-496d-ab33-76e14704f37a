
import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { CalendarIcon, Search, Download, Eye } from "lucide-react";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

// Mock data for dropdowns
const mockUsers = [
  { id: "1", name: "<PERSON>" },
  { id: "2", name: "<PERSON>" },
  { id: "3", name: "<PERSON>" },
  { id: "4", name: "<PERSON>" }
];

const mockKnowledgeAreas = [
  { id: "1", name: "Computer Science" },
  { id: "2", name: "<PERSON>" },
  { id: "3", name: "<PERSON>" }
];

const mockKnowledgeTopics = [
  { id: "1", name: "Data Structures" },
  { id: "2", name: "Algorithms" },
  { id: "3", name: "Database Systems" }
];

const mockKnowledgeUnits = [
  { id: "1", name: "Arrays and Linked Lists" },
  { id: "2", name: "Sorting Algorithms" },
  { id: "3", name: "SQL Fundamentals" }
];

// Mock report data
const mockReportData = [
  {
    id: "1",
    userName: "John Doe",
    userId: "U123",
    knowledgeArea: "Computer Science",
    knowledgeTopic: "Data Structures",
    knowledgeUnits: "Arrays and Linked Lists",
    knowledgeIndex: "85%",
    timeElapsed: "45 minutes",
    rating: "4.5/5",
    remarks: "Excellent understanding",
    geoLocation: "New York, USA",
    date: "2025-04-02"
  },
  {
    id: "2",
    userName: "Jane Smith",
    userId: "U124",
    knowledgeArea: "Mathematics",
    knowledgeTopic: "Calculus",
    knowledgeUnits: "Differentiation",
    knowledgeIndex: "92%",
    timeElapsed: "38 minutes",
    rating: "4.8/5",
    remarks: "Outstanding performance",
    geoLocation: "London, UK",
    date: "2025-04-01"
  },
  {
    id: "3",
    userName: "Robert Johnson",
    userId: "U125",
    knowledgeArea: "Biology",
    knowledgeTopic: "Genetics",
    knowledgeUnits: "DNA Structure",
    knowledgeIndex: "78%",
    timeElapsed: "52 minutes",
    rating: "4.0/5",
    remarks: "Good, needs improvement in some areas",
    geoLocation: "Toronto, Canada",
    date: "2025-03-30"
  }
];

export default function Reports() {
  const [reportType, setReportType] = useState<string>("user");
  const [selectedUser, setSelectedUser] = useState<string>("");
  const [selectedArea, setSelectedArea] = useState<string>("");
  const [selectedTopic, setSelectedTopic] = useState<string>("");
  const [selectedUnit, setSelectedUnit] = useState<string>("");
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined,
  });
  const [searchClicked, setSearchClicked] = useState<boolean>(false);

  const handleSearch = () => {
    setSearchClicked(true);
    // In a real application, this would fetch data based on selected filters
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
      </div>

      <Card className="p-6">
        <div className="space-y-4">
          <div>
            <h2 className="text-xl font-semibold mb-3">Report Selection</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
              <div className="flex items-center space-x-2">
                <input 
                  type="radio" 
                  id="user" 
                  name="reportType" 
                  value="user"
                  checked={reportType === "user"}
                  onChange={() => setReportType("user")}
                  className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                />
                <label htmlFor="user" className="text-sm font-medium">User</label>
              </div>
              <div className="flex items-center space-x-2">
                <input 
                  type="radio" 
                  id="knowledgeArea" 
                  name="reportType" 
                  value="knowledgeArea"
                  checked={reportType === "knowledgeArea"}
                  onChange={() => setReportType("knowledgeArea")}
                  className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                />
                <label htmlFor="knowledgeArea" className="text-sm font-medium">Knowledge Area</label>
              </div>
              <div className="flex items-center space-x-2">
                <input 
                  type="radio" 
                  id="knowledgeTopic" 
                  name="reportType" 
                  value="knowledgeTopic"
                  checked={reportType === "knowledgeTopic"}
                  onChange={() => setReportType("knowledgeTopic")}
                  className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                />
                <label htmlFor="knowledgeTopic" className="text-sm font-medium">Knowledge Topic</label>
              </div>
              <div className="flex items-center space-x-2">
                <input 
                  type="radio" 
                  id="knowledgeUnits" 
                  name="reportType" 
                  value="knowledgeUnits"
                  checked={reportType === "knowledgeUnits"}
                  onChange={() => setReportType("knowledgeUnits")}
                  className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                />
                <label htmlFor="knowledgeUnits" className="text-sm font-medium">Knowledge Units</label>
              </div>
              <div className="flex items-center space-x-2">
                <input 
                  type="radio" 
                  id="overallReport" 
                  name="reportType" 
                  value="overallReport"
                  checked={reportType === "overallReport"}
                  onChange={() => setReportType("overallReport")}
                  className="h-4 w-4 border-gray-300 text-primary focus:ring-primary"
                />
                <label htmlFor="overallReport" className="text-sm font-medium">Overall Report</label>
              </div>
            </div>

            <Separator className="my-4" />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {reportType === "user" && (
                <div>
                  <label className="block text-sm font-medium mb-1">Select User</label>
                  <Select onValueChange={setSelectedUser} value={selectedUser}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a user" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockUsers.map((user) => (
                        <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {reportType === "knowledgeArea" && (
                <div>
                  <label className="block text-sm font-medium mb-1">Select Knowledge Area</label>
                  <Select onValueChange={setSelectedArea} value={selectedArea}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a knowledge area" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockKnowledgeAreas.map((area) => (
                        <SelectItem key={area.id} value={area.id}>{area.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {reportType === "knowledgeTopic" && (
                <div>
                  <label className="block text-sm font-medium mb-1">Select Knowledge Topic</label>
                  <Select onValueChange={setSelectedTopic} value={selectedTopic}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a knowledge topic" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockKnowledgeTopics.map((topic) => (
                        <SelectItem key={topic.id} value={topic.id}>{topic.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {reportType === "knowledgeUnits" && (
                <div>
                  <label className="block text-sm font-medium mb-1">Select Knowledge Unit</label>
                  <Select onValueChange={setSelectedUnit} value={selectedUnit}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a knowledge unit" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockKnowledgeUnits.map((unit) => (
                        <SelectItem key={unit.id} value={unit.id}>{unit.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="col-span-1 md:col-span-2 lg:col-span-1">
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Date Range</label>
                  <div className="grid grid-cols-2 gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !dateRange.from && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange.from ? (
                            format(dateRange.from, "PPP")
                          ) : (
                            <span>Start date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={dateRange.from}
                          onSelect={(date) => setDateRange({ ...dateRange, from: date })}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal",
                            !dateRange.to && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateRange.to ? (
                            format(dateRange.to, "PPP")
                          ) : (
                            <span>End date</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="end">
                        <Calendar
                          mode="single"
                          selected={dateRange.to}
                          onSelect={(date) => setDateRange({ ...dateRange, to: date })}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              </div>

              <div className="col-span-1 md:col-span-2 lg:col-span-1 flex items-end">
                <Button className="w-full" onClick={handleSearch}>
                  <Search className="mr-2 h-4 w-4" />
                  Search
                </Button>
              </div>
            </div>
          </div>

          {searchClicked && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-3">Report Results</h3>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User Name</TableHead>
                      <TableHead>User ID</TableHead>
                      <TableHead>Knowledge Area</TableHead>
                      <TableHead>Knowledge Topic</TableHead>
                      <TableHead>Knowledge Units</TableHead>
                      <TableHead>Knowledge Index</TableHead>
                      <TableHead>Time Elapsed</TableHead>
                      <TableHead>Rating</TableHead>
                      <TableHead>Remarks</TableHead>
                      <TableHead>Geo Location</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Session Report</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockReportData.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>{report.userName}</TableCell>
                        <TableCell>{report.userId}</TableCell>
                        <TableCell>{report.knowledgeArea}</TableCell>
                        <TableCell>{report.knowledgeTopic}</TableCell>
                        <TableCell>{report.knowledgeUnits}</TableCell>
                        <TableCell>{report.knowledgeIndex}</TableCell>
                        <TableCell>{report.timeElapsed}</TableCell>
                        <TableCell>{report.rating}</TableCell>
                        <TableCell>{report.remarks}</TableCell>
                        <TableCell>{report.geoLocation}</TableCell>
                        <TableCell>{report.date}</TableCell>
                        <TableCell>
                          <div className="flex space-x-2">
                            <Button variant="ghost" size="icon">
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
