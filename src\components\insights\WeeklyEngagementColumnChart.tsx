
import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { weeklyEngagementData } from '@/data/analyticsData';

const WeeklyEngagementColumnChart: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Weekly Engagement (Hours)</CardTitle>
      </CardHeader>
      <CardContent className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={weeklyEngagementData}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="day" />
            <YAxis />
            <Tooltip formatter={(value) => [`${value} hrs`, 'Time Spent']} />
            <Legend />
            <Bar dataKey="hours" name="Hours Spent" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

export default WeeklyEngagementColumnChart;
