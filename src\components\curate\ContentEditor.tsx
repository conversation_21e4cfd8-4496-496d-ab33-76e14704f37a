import React, { useState } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { ContentMode, DroppedItem, ContentComponent } from "@/types";
import Sidebar from "@/components/Sidebar";
import DroppableArea from "@/components/DroppableArea";
import { v4 as uuidv4 } from "uuid";

interface ContentEditorProps {
  open: boolean;
  onClose: () => void;
}

export default function ContentEditor({ open, onClose }: ContentEditorProps) {
  const [activeMode, setActiveMode] = useState<ContentMode>("communicate");
  const [items, setItems] = useState<DroppedItem[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  const handleModeChange = (mode: ContentMode) => {
    setActiveMode(mode);
  };

  const handleDragStart = (type: string) => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleAddItem = (item: DroppedItem) => {
    setItems((prev) => [...prev, item]);
  };

  const handleRemoveItem = (id: string) => {
    setItems((prev) => prev.filter((item) => item.id !== id));
  };

  const handleUpdateItem = (id: string, data: ContentComponent) => {
    setItems((prev) =>
      prev.map((item) => (item.id === id ? { ...item, data } : item))
    );
  };

  const handleReorderItems = (sourceId: string, targetId: string) => {
    setItems((prev) => {
      const sourceIndex = prev.findIndex((item) => item.id === sourceId);
      const targetIndex = prev.findIndex((item) => item.id === targetId);

      if (sourceIndex === -1 || targetIndex === -1) return prev;

      const newItems = [...prev];
      const [movedItem] = newItems.splice(sourceIndex, 1);
      newItems.splice(targetIndex, 0, movedItem);

      // Update position property for all items
      return newItems.map((item, index) => ({
        ...item,
        data: { ...item.data, position: index }
      }));
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl w-[95vw] h-[95vh] overflow-hidden p-0 flex flex-col rounded-lg shadow-xl border-0">
        <div
          className="flex flex-1 overflow-hidden bg-slate-50 dark:bg-slate-900"
          onDragEnd={handleDragEnd}
        >
          <Sidebar
            activeMode={activeMode}
            onDragStart={handleDragStart}
            onChange={handleModeChange}
          />

          <DroppableArea
            activeMode={activeMode}
            items={items}
            onAddItem={handleAddItem}
            onRemoveItem={handleRemoveItem}
            onUpdateItem={handleUpdateItem}
            onReorderItems={handleReorderItems}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
