
import React, { useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { User, Search, MoreHorizontal, Upload, Download, Plus } from "lucide-react";
import { Card } from "@/components/ui/card";
import UserFilters from "@/components/users/UserFilters";
import AddUserForm from "@/components/users/AddUserForm";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";

// Mock user data
const mockUsers = [
  {
    id: "1",
    name: "<PERSON>",
    employeeNo: "EMP-001",
    email: "<EMAIL>",
    phone: "******-567-8901",
    dateOfJoining: "2021-05-12",
    division: "Clinical Support",
    department: "Biomedical",
    location: "KHS",
    pullRate: 85,
    knowledgeIndex: 92,
    status: "active",
    groups: ["KCN-Operations-Facility Services", "KCN-Information Technology-Information Technology"],
  },
  {
    id: "2",
    name: "Jamie Smith",
    employeeNo: "EMP-002",
    email: "<EMAIL>",
    phone: "******-567-8902",
    dateOfJoining: "2022-02-15",
    division: "Facility Services",
    department: "Engineering",
    location: "KCN",
    pullRate: 78,
    knowledgeIndex: 81,
    status: "active",
  },
  {
    id: "3",
    name: "Taylor Brown",
    employeeNo: "EMP-003",
    email: "<EMAIL>",
    phone: "******-567-8903",
    dateOfJoining: "2020-11-03",
    division: "Facility Services",
    department: "Facility Services",
    location: "KCN",
    pullRate: 92,
    knowledgeIndex: 88,
    status: "inactive",
  },
  {
    id: "4",
    name: "Morgan Williams",
    employeeNo: "EMP-004",
    email: "<EMAIL>",
    phone: "******-567-8904",
    dateOfJoining: "2023-01-20",
    division: "Clinical Nursing",
    department: "OPD",
    location: "KCN",
    pullRate: 65,
    knowledgeIndex: 72,
    status: "active",
  },
  {
    id: "5",
    name: "Casey Davis",
    employeeNo: "EMP-005",
    email: "<EMAIL>",
    phone: "******-567-8905",
    dateOfJoining: "2022-07-08",
    division: "Operations",
    department: "OPD",
    location: "KTN",
    pullRate: 89,
    knowledgeIndex: 94,
    status: "blocked",
  },
  {
    id: "6",
    name: "Jordan Miller",
    employeeNo: "EMP-006",
    email: "<EMAIL>",
    phone: "******-567-8906",
    dateOfJoining: "2021-09-14",
    division: "Facility Services",
    department: "Facility Services",
    location: "KTN",
    pullRate: 91,
    knowledgeIndex: 87,
    status: "active",
  },
];

// Mock external users
const mockExternalUsers = [
  {
    id: "e1",
    name: "Robin Lee",
    employeeNo: "EXT-001",
    email: "<EMAIL>",
    phone: "******-567-7001",
    dateOfJoining: "2022-06-10",
    division: "Clinical Nursing",
    department: "Nursing",
    location: "KTN",
    pullRate: 75,
    knowledgeIndex: 80,
    status: "active",
    groups: ["KCN-Clinical Research-Clinical Research"],
  },
  {
    id: "e2",
    name: "Sam Green",
    employeeNo: "EXT-002",
    email: "<EMAIL>",
    phone: "******-567-7002",
    dateOfJoining: "2021-10-05",
    division: "Clinical Support",
    department: "Critical Care",
    location: "KTN",
    pullRate: 68,
    knowledgeIndex: 77,
    status: "active",
  },
];

export default function Users() {
  const [activeTab, setActiveTab] = useState("internal");
  const [searchTerm, setSearchTerm] = useState("");
  const [addUserOpen, setAddUserOpen] = useState(false);
  const { toast } = useToast();

  // Extract unique values for filters
  const uniqueDivisions = useMemo(() =>
    Array.from(new Set([...mockUsers, ...mockExternalUsers].map(user => user.division))),
    []
  );

  const uniqueDepartments = useMemo(() =>
    Array.from(new Set([...mockUsers, ...mockExternalUsers].map(user => user.department))),
    []
  );

  const uniqueLocations = useMemo(() =>
    Array.from(new Set([...mockUsers, ...mockExternalUsers].map(user => user.location))),
    []
  );

  const uniqueStatuses = useMemo(() =>
    Array.from(new Set([...mockUsers, ...mockExternalUsers].map(user => user.status))),
    []
  );

  // Filter state
  const [filters, setFilters] = useState({
    division: "",
    department: "",
    location: "",
    status: "",
    startDate: undefined as Date | undefined,
    endDate: undefined as Date | undefined,
  });

  const resetFilters = () => {
    setFilters({
      division: "",
      department: "",
      location: "",
      status: "",
      startDate: undefined,
      endDate: undefined,
    });
  };

  // Filter users based on search term and filters
  const filterUsers = (users: typeof mockUsers) => {
    return users.filter((user) => {
      // Search term filter
      const matchesSearch =
        searchTerm === "" ||
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.employeeNo.toLowerCase().includes(searchTerm.toLowerCase());

      // Column filters
      const matchesDivision = filters.division === "" || user.division === filters.division;
      const matchesDepartment = filters.department === "" || user.department === filters.department;
      const matchesLocation = filters.location === "" || user.location === filters.location;
      const matchesStatus = filters.status === "" || user.status === filters.status;

      // Date range filter
      let matchesDateRange = true;
      const userJoiningDate = new Date(user.dateOfJoining);

      if (filters.startDate) {
        matchesDateRange = matchesDateRange && userJoiningDate >= filters.startDate;
      }

      if (filters.endDate) {
        matchesDateRange = matchesDateRange && userJoiningDate <= filters.endDate;
      }

      return matchesSearch && matchesDivision && matchesDepartment && matchesLocation && matchesStatus && matchesDateRange;
    });
  };

  const filteredInternalUsers = filterUsers(mockUsers);
  const filteredExternalUsers = filterUsers(mockExternalUsers);

  const handleAddUser = (userData: any) => {
    // In a real application, you would send this to your API
    // Set default status to active for new users
    const newUser = {
      ...userData,
      status: "active"
    };

    console.log("Adding user:", newUser);

    toast({
      title: "Success",
      description: `${newUser.name} has been added successfully.`,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-500";
      case "inactive":
        return "bg-yellow-500";
      case "blocked":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  // Function to handle blocking a user
  const handleBlockUser = (userId: string, userType: "internal" | "external") => {
    // In a real application, you would send this to your API
    if (userType === "internal") {
      // Find and update the user in mockUsers
      const userIndex = mockUsers.findIndex(user => user.id === userId);
      if (userIndex !== -1) {
        mockUsers[userIndex].status = "blocked";
        // Force a re-render
        setActiveTab(activeTab);

        toast({
          title: "User Blocked",
          description: `${mockUsers[userIndex].name} has been blocked.`,
        });
      }
    } else {
      // Find and update the user in mockExternalUsers
      const userIndex = mockExternalUsers.findIndex(user => user.id === userId);
      if (userIndex !== -1) {
        mockExternalUsers[userIndex].status = "blocked";
        // Force a re-render
        setActiveTab(activeTab);

        toast({
          title: "User Blocked",
          description: `${mockExternalUsers[userIndex].name} has been blocked.`,
        });
      }
    }
  };

  // Function to handle activating a user
  const handleActivateUser = (userId: string, userType: "internal" | "external") => {
    // In a real application, you would send this to your API
    if (userType === "internal") {
      // Find and update the user in mockUsers
      const userIndex = mockUsers.findIndex(user => user.id === userId);
      if (userIndex !== -1) {
        mockUsers[userIndex].status = "active";
        // Force a re-render
        setActiveTab(activeTab);

        toast({
          title: "User Activated",
          description: `${mockUsers[userIndex].name} has been activated.`,
        });
      }
    } else {
      // Find and update the user in mockExternalUsers
      const userIndex = mockExternalUsers.findIndex(user => user.id === userId);
      if (userIndex !== -1) {
        mockExternalUsers[userIndex].status = "active";
        // Force a re-render
        setActiveTab(activeTab);

        toast({
          title: "User Activated",
          description: `${mockExternalUsers[userIndex].name} has been activated.`,
        });
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Users Management</h1>
      </div>

      <Tabs defaultValue="internal" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="internal">Internal Users</TabsTrigger>
          <TabsTrigger value="external">External Users</TabsTrigger>
        </TabsList>

        <TabsContent value="internal" className="mt-6 space-y-4">
          <Card className="p-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" className="gap-2">
                  <Upload size={16} />
                  Import
                </Button>
                <Button variant="outline" className="gap-2">
                  <Download size={16} />
                  Export
                </Button>
                <Button className="gap-2" onClick={() => setAddUserOpen(true)}>
                  <Plus size={16} />
                  Add User
                </Button>
              </div>
            </div>

            <UserFilters
              divisions={uniqueDivisions}
              departments={uniqueDepartments}
              locations={uniqueLocations}
              statuses={uniqueStatuses}
              filters={filters}
              setFilters={setFilters}
              onResetFilters={resetFilters}
            />

            <div className="rounded-lg border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Employee No.</TableHead>
                    <TableHead className="hidden md:table-cell">Email</TableHead>
                    <TableHead className="hidden lg:table-cell">Phone</TableHead>
                    <TableHead className="hidden lg:table-cell">DOJ</TableHead>
                    <TableHead className="hidden md:table-cell">Division</TableHead>
                    <TableHead className="hidden lg:table-cell">Department</TableHead>
                    <TableHead className="hidden md:table-cell">Location</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredInternalUsers.length > 0 ? (
                    filteredInternalUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.employeeNo}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.email}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.phone}</TableCell>
                        <TableCell className="hidden lg:table-cell">{format(new Date(user.dateOfJoining), 'dd/MM/yyyy')}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.division}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.department}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.location}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className="capitalize"
                          >
                            <span className={`mr-1.5 h-2 w-2 rounded-full ${getStatusColor(user.status)}`}></span>
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="View Details">
                              <User className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="Edit User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path><path d="m15 5 4 4"></path></svg>
                            </Button>
                            {user.status === "active" ? (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-orange-600"
                                title="Block User"
                                onClick={() => handleBlockUser(user.id, "internal")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><path d="M9 9h6v6H9z"></path></svg>
                              </Button>
                            ) : (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-green-600"
                                title="Activate User"
                                onClick={() => handleActivateUser(user.id, "internal")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                              </Button>
                            )}
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-red-600" title="Delete User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={9} className="text-center py-10 text-muted-foreground">
                        No users found matching your search criteria.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </Card>

          <AddUserForm
            open={addUserOpen && activeTab === "internal"}
            onOpenChange={setAddUserOpen}
            onAddUser={handleAddUser}
            divisions={uniqueDivisions}
            departments={uniqueDepartments}
            locations={uniqueLocations}
            userType="internal"
          />
        </TabsContent>

        <TabsContent value="external" className="mt-6">
          <Card className="p-4">
            <div className="flex flex-col sm:flex-row justify-between gap-4 mb-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search external users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex flex-wrap gap-2">
                <Button className="gap-2" onClick={() => setAddUserOpen(true)}>
                  <Plus size={16} />
                  Add External User
                </Button>
              </div>
            </div>

            <UserFilters
              divisions={uniqueDivisions}
              departments={uniqueDepartments}
              locations={uniqueLocations}
              statuses={uniqueStatuses}
              filters={filters}
              setFilters={setFilters}
              onResetFilters={resetFilters}
            />

            {filteredExternalUsers.length > 0 ? (
              <div className="rounded-lg border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>External ID</TableHead>
                      <TableHead className="hidden md:table-cell">Email</TableHead>
                      <TableHead className="hidden lg:table-cell">Phone</TableHead>
                      <TableHead className="hidden lg:table-cell">DOJ</TableHead>
                      <TableHead className="hidden md:table-cell">Division</TableHead>
                      <TableHead className="hidden lg:table-cell">Department</TableHead>
                      <TableHead className="hidden md:table-cell">Location</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredExternalUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell>{user.employeeNo}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.email}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.phone}</TableCell>
                        <TableCell className="hidden lg:table-cell">{format(new Date(user.dateOfJoining), 'dd/MM/yyyy')}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.division}</TableCell>
                        <TableCell className="hidden lg:table-cell">{user.department}</TableCell>
                        <TableCell className="hidden md:table-cell">{user.location}</TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className="capitalize"
                          >
                            <span className={`mr-1.5 h-2 w-2 rounded-full ${getStatusColor(user.status)}`}></span>
                            {user.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="View Details">
                              <User className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon" className="h-8 w-8" title="Edit User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path><path d="m15 5 4 4"></path></svg>
                            </Button>
                            {user.status === "active" ? (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-orange-600"
                                title="Block User"
                                onClick={() => handleBlockUser(user.id, "external")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><path d="M9 9h6v6H9z"></path></svg>
                              </Button>
                            ) : (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 text-green-600"
                                title="Activate User"
                                onClick={() => handleActivateUser(user.id, "external")}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>
                              </Button>
                            )}
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-red-600" title="Delete User">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-4 w-4"><path d="M3 6h18"></path><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path></svg>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-10">
                <User size={48} className="mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No external users found</h3>
                <p className="text-muted-foreground mb-4">
                  No users match your current search criteria.
                </p>
              </div>
            )}
          </Card>

          <AddUserForm
            open={addUserOpen && activeTab === "external"}
            onOpenChange={setAddUserOpen}
            onAddUser={handleAddUser}
            divisions={uniqueDivisions}
            departments={uniqueDepartments}
            locations={uniqueLocations}
            userType="external"
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
