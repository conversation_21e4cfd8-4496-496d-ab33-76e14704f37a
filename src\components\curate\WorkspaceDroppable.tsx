import React from 'react';
import { useDroppable } from '@dnd-kit/core';
import { cn } from '@/lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

interface WorkspaceDroppableProps {
  id: string;
  children: React.ReactNode;
  className?: string;
}

export function WorkspaceDroppable({ id, children, className }: WorkspaceDroppableProps) {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  return (
    <div
      ref={setNodeRef}
      className={cn(
        'transition-all duration-300 ease-in-out',
        isOver ? 'bg-primary/10 border-primary/30 scale-[1.01] shadow-lg' : '',
        className
      )}
    >
      {children}
    </div>
  );
}

export function WorkspaceDropIndicator({ isVisible }: { isVisible: boolean }) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: '4px' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.2 }}
          className="w-full bg-primary rounded-full my-1 animate-pulse shadow-glow"
        />
      )}
    </AnimatePresence>
  );
}
