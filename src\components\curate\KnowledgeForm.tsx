
import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";

interface KnowledgeFormProps {
  open: boolean;
  type: "area" | "topic" | "unit";
  isEditing: boolean;
  defaultValues?: {
    id?: string;
    name?: string;
    description?: string;
  };
  onClose: () => void;
  onSubmit: (values: { name: string; description: string }) => void;
}

export default function KnowledgeForm({
  open,
  type,
  isEditing,
  defaultValues,
  onClose,
  onSubmit,
}: KnowledgeFormProps) {
  const [name, setName] = React.useState(defaultValues?.name || "");
  const [description, setDescription] = React.useState(
    defaultValues?.description || ""
  );

  React.useEffect(() => {
    if (open) {
      setName(defaultValues?.name || "");
      setDescription(defaultValues?.description || "");
    }
  }, [open, defaultValues]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ name, description });
  };

  const titleMap = {
    area: "Knowledge Area",
    topic: "Knowledge Topic",
    unit: "Knowledge Unit",
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {isEditing ? `Edit ${titleMap[type]}` : `Add ${titleMap[type]}`}
          </DialogTitle>
          <DialogDescription>
            {isEditing
              ? `Update the details for this ${type}.`
              : `Create a new ${type}.`}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder={`Enter ${type} name`}
                required
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder={`Enter ${type} description`}
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              {isEditing ? "Update" : "Create"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
