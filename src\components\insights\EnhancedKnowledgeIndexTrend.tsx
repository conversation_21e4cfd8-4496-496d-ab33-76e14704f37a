import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { motion } from 'framer-motion';
import { TrendingUp } from 'lucide-react';

interface EnhancedKnowledgeIndexTrendProps {
  selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  };
}

// Mock data for knowledge index trend
const getKnowledgeIndexTrend = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return [
    { month: 'Jan', index: 65, target: 70 },
    { month: 'Feb', index: 68, target: 70 },
    { month: 'Mar', index: 70, target: 70 },
    { month: 'Apr', index: 72, target: 75 },
    { month: 'May', index: 75, target: 75 },
    { month: 'Jun', index: 74, target: 75 },
    { month: 'Jul', index: 76, target: 80 },
    { month: 'Aug', index: 78, target: 80 },
    { month: 'Sep', index: 80, target: 80 },
    { month: 'Oct', index: 82, target: 85 },
    { month: 'Nov', index: 85, target: 85 },
    { month: 'Dec', index: 88, target: 85 },
  ];
};

const EnhancedKnowledgeIndexTrend: React.FC<EnhancedKnowledgeIndexTrendProps> = ({ selection }) => {
  const [chartData, setChartData] = useState<any[]>([]);
  const data = getKnowledgeIndexTrend(selection);
  
  // Animate the data on mount
  useEffect(() => {
    // Start with zero values
    const initialData = data.map(item => ({
      ...item,
      index: 0,
      target: 0
    }));
    
    setChartData(initialData);
    
    // Animate to actual values
    const timer = setTimeout(() => {
      setChartData(data);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [data]);
  
  // Calculate the current month's index and the change from previous month
  const currentMonth = data[data.length - 1];
  const previousMonth = data[data.length - 2];
  const change = currentMonth.index - previousMonth.index;
  const changePercentage = ((change / previousMonth.index) * 100).toFixed(1);
  const isPositive = change >= 0;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card className="h-full">
        <CardHeader className="pb-2">
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-indigo-500" />
              Knowledge Index Trend
            </CardTitle>
            <motion.div 
              className={`px-3 py-1 rounded-full text-sm font-medium ${isPositive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.5, duration: 0.3 }}
            >
              {isPositive ? '+' : ''}{changePercentage}% from last month
            </motion.div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-6">
            <motion.div 
              className="text-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
            >
              <p className="text-sm text-gray-500">Current Index</p>
              <p className="text-3xl font-bold">{currentMonth.index}%</p>
            </motion.div>
            <motion.div 
              className="text-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.3 }}
            >
              <p className="text-sm text-gray-500">Target</p>
              <p className="text-3xl font-bold">{currentMonth.target}%</p>
            </motion.div>
            <motion.div 
              className="text-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.3 }}
            >
              <p className="text-sm text-gray-500">Change</p>
              <p className={`text-3xl font-bold ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                {isPositive ? '+' : ''}{change}%
              </p>
            </motion.div>
          </div>
          
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis 
                  dataKey="month" 
                  axisLine={{ stroke: '#E5E7EB' }}
                  tickLine={false}
                />
                <YAxis 
                  domain={[0, 100]} 
                  axisLine={false}
                  tickLine={false}
                  tickFormatter={(value) => `${value}%`}
                />
                <Tooltip 
                  formatter={(value) => [`${value}%`, '']}
                  contentStyle={{ 
                    borderRadius: '8px', 
                    border: 'none', 
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    padding: '12px'
                  }}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="index"
                  name="Knowledge Index"
                  stroke="#3B82F6"
                  strokeWidth={3}
                  dot={{ r: 4, strokeWidth: 2 }}
                  activeDot={{ r: 6 }}
                  animationDuration={1500}
                  animationEasing="ease-in-out"
                />
                <Line
                  type="monotone"
                  dataKey="target"
                  name="Target"
                  stroke="#EF4444"
                  strokeWidth={2}
                  strokeDasharray="5 5"
                  dot={false}
                  animationDuration={1500}
                  animationEasing="ease-in-out"
                  animationBegin={300}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default EnhancedKnowledgeIndexTrend;
