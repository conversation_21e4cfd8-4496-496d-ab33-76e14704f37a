import React, { useState } from "react";
import { ChevronRight, ChevronDown, Plus, Edit2, Trash, Wand2, Co<PERSON> } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface ChecklistFormArea {
  id: string;
  name: string;
  type: "area";
  topicCount: number;
  children: ChecklistFormTopic[];
}

interface ChecklistFormTopic {
  id: string;
  name: string;
  type: "topic";
  unitCount: number;
  children: ChecklistFormUnit[];
}

interface ChecklistFormUnit {
  id: string;
  name: string;
  type: "unit";
  category: "checklist" | "form";
  itemCount: number;
  createdOn: string;
  updatedOn: string;
  isDefault?: boolean;
}

type ChecklistFormItem = ChecklistFormArea | ChecklistFormTopic | ChecklistFormUnit;

interface KnowledgeTreeProps {
  data: ChecklistFormItem[];
  onAdd: (type: "area" | "topic" | "unit") => void;
  onEdit: (item: ChecklistFormItem) => void;
  onDelete: (item: ChecklistFormItem) => void;
  onCurate?: (itemId: string) => void;
  curateItemId?: string | null;
  mode?: "knowledge" | "checklistform";
}

export default function KnowledgeTree({
  data,
  onAdd,
  onEdit,
  onDelete,
  onCurate,
  curateItemId,
  mode = "knowledge"
}: KnowledgeTreeProps) {
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});

  const toggleExpand = (id: string) => {
    setExpandedItems((prev) => ({ ...prev, [id]: !prev[id] }));
  };

  const handleCurateClick = (itemId: string) => {
    if (onCurate) {
      onCurate(itemId);
    }
  };

  const getCircleColorClass = (level: number, index: number = 0, item?: ChecklistFormItem) => {
    if (item?.type === "unit") {
      return item.category === "checklist" ? "bg-blue-600" : "bg-green-600";
    }

    if (level === 0) { // Area level
      return "bg-purple-600";
    } else if (level === 1) { // Topic level
      const colors = [
        "bg-purple-600", // purple
        "bg-gray-600",   // gray
        "bg-red-600",    // red
      ];
      return colors[index % colors.length];
    } else { // Unit level
      return "bg-purple-600"; // default purple for units
    }
  };

  const renderTreeItem = (item: ChecklistFormItem, level: number = 0, index: number = 0) => {
    const hasChildren = item.type !== "unit" && (item as ChecklistFormArea | ChecklistFormTopic).children.length > 0;
    const isExpanded = expandedItems[item.id];
    const isCurating = curateItemId === item.id;

    return (
      <div key={item.id} className="ml-4 first:ml-0">
        <div className="flex items-center py-2 gap-2 group hover:bg-gray-50 border-b">
          <div className="w-5">
            {hasChildren && (
              <button
                onClick={() => toggleExpand(item.id)}
                className="text-gray-500 hover:text-gray-700"
              >
                {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </button>
            )}
          </div>

          <div className="flex-shrink-0 mr-3">
            <div className={`h-10 w-10 rounded-full ${getCircleColorClass(level, index, item)} flex items-center justify-center text-white font-semibold`}>
              {item.type === "unit" ? (item.category === "checklist" ? "C" : "F") : index + 1}
            </div>
          </div>

          <div className="flex-1">
            <div className="text-sm font-medium flex items-center">
              {item.name}
              {item.type === "unit" && (item as ChecklistFormUnit).isDefault && (
                <Badge variant="outline" className="ml-2 text-xs">Default</Badge>
              )}
            </div>
            {item.type === "unit" && (
              <div className="text-xs text-gray-500">
                {(item as ChecklistFormUnit).itemCount} Item(s) • Created on: {(item as ChecklistFormUnit).createdOn}
                <br />
                Updated on: {(item as ChecklistFormUnit).updatedOn}
              </div>
            )}
            {item.type !== "unit" && (
              <div className="text-xs text-gray-500">
                {item.type === "area" ? (item as ChecklistFormArea).topicCount : (item as ChecklistFormTopic).unitCount} {item.type === "area" ? "Topic(s)" : "Unit(s)"}
              </div>
            )}
          </div>

          <div className="ml-2 flex items-center space-x-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-purple-700"
              onClick={() => onEdit(item)}
            >
              <Edit2 size={16} />
            </Button>
            {item.type === "unit" && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-gray-500 hover:text-purple-700"
              >
                <Copy size={16} />
              </Button>
            )}
            {item.type === "unit" && (
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 text-purple-600 hover:text-purple-800"
                onClick={() => handleCurateClick(item.id)}
              >
                <Wand2 size={16} />
              </Button>
            )}
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 text-gray-500 hover:text-red-600"
              onClick={() => onDelete(item)}
            >
              <Trash size={16} />
            </Button>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="ml-4">
            {(item as ChecklistFormArea | ChecklistFormTopic).children.map((child, childIndex) =>
              renderTreeItem(child, level + 1, childIndex)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-2">
      {data.map((item, index) => renderTreeItem(item, 0, index))}
      <Button
        variant="outline"
        onClick={() => onAdd("area")}
        className="mt-4 w-full"
      >
        <Plus size={16} className="mr-2" />
        Add Area
      </Button>
    </div>
  );
}
