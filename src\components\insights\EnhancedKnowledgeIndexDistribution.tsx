import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON> as PieChartIcon } from 'lucide-react';

interface EnhancedKnowledgeIndexDistributionProps {
  selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  };
}

// Mock data for knowledge index distribution
const getKnowledgeDistribution = (selection: {
  area: string | null;
  topic: string | null;
  units: string[];
}) => {
  // In a real application, this would be calculated based on actual data
  // For now, we'll return mock data
  return [
    { name: 'Expert', value: 20, color: '#10B981' },
    { name: 'Proficient', value: 30, color: '#3B82F6' },
    { name: 'Competent', value: 25, color: '#F59E0B' },
    { name: '<PERSON><PERSON><PERSON>', value: 15, color: '#EF4444' },
    { name: 'No Knowledge', value: 10, color: '#6B7280' },
  ];
};

// Custom label for the pie chart
const RADIAN = Math.PI / 180;
const renderCustomizedLabel = ({
  cx,
  cy,
  midAngle,
  innerRadius,
  outerRadius,
  percent,
  name,
}: any) => {
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text 
      x={x} 
      y={y} 
      fill="white" 
      textAnchor={x > cx ? 'start' : 'end'} 
      dominantBaseline="central"
      style={{ fontWeight: 'bold', fontSize: '12px' }}
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const EnhancedKnowledgeIndexDistribution: React.FC<EnhancedKnowledgeIndexDistributionProps> = ({ selection }) => {
  const [chartData, setChartData] = useState<any[]>([]);
  const data = getKnowledgeDistribution(selection);
  
  // Animate the data on mount
  useEffect(() => {
    // Start with equal values
    const total = data.reduce((sum, item) => sum + item.value, 0);
    const equalValue = total / data.length;
    
    const initialData = data.map(item => ({
      ...item,
      value: 0
    }));
    
    setChartData(initialData);
    
    // Animate to actual values
    const timer = setTimeout(() => {
      setChartData(data);
    }, 500);
    
    return () => clearTimeout(timer);
  }, [data]);
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="h-full"
    >
      <Card className="h-full">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2">
            <PieChartIcon className="h-5 w-5 text-indigo-500" />
            Knowledge Index Distribution
          </CardTitle>
        </CardHeader>
        <CardContent className="h-[350px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={renderCustomizedLabel}
                outerRadius={120}
                innerRadius={60}
                fill="#8884d8"
                dataKey="value"
                animationDuration={1500}
                animationEasing="ease-in-out"
              >
                {chartData.map((entry, index) => (
                  <Cell 
                    key={`cell-${index}`} 
                    fill={entry.color} 
                  />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value: number) => `${value}%`}
                contentStyle={{ 
                  borderRadius: '8px', 
                  border: 'none', 
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  padding: '12px'
                }}
              />
              <Legend 
                layout="horizontal" 
                verticalAlign="bottom" 
                align="center"
                wrapperStyle={{ paddingTop: '20px' }}
              />
            </PieChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default EnhancedKnowledgeIndexDistribution;
