import React from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface OrganizationCheckboxListProps {
  title: string;
  options: string[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  disabled?: boolean;
  maxHeight?: string;
}

export function OrganizationCheckboxList({
  title,
  options,
  selectedValues,
  onChange,
  disabled = false,
  maxHeight = "200px"
}: OrganizationCheckboxListProps) {
  const allSelected = options.length > 0 && selectedValues.length === options.length;

  const handleSelectAll = () => {
    onChange([...options]);
  };

  const handleClearAll = () => {
    onChange([]);
  };

  const handleToggleItem = (item: string, checked: boolean) => {
    if (checked) {
      onChange([...selectedValues, item]);
    } else {
      onChange(selectedValues.filter(i => i !== item));
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-2">
        <div className="flex items-center">
          {title !== "Business Units" && (
            <div className="w-5 h-5 flex items-center justify-center mr-2">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5L12 19" stroke={disabled ? "#d1d5db" : "#6b7280"} strokeWidth="2" strokeLinecap="round"/>
                <path d="M19 12L5 12" stroke={disabled ? "#d1d5db" : "#6b7280"} strokeWidth="2" strokeLinecap="round"/>
              </svg>
            </div>
          )}
          <h3 className={`text-base font-medium ${disabled ? 'text-gray-400' : ''}`}>{title}</h3>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleClearAll}
          disabled={disabled || selectedValues.length === 0}
          className="h-7 text-xs px-3 py-1 rounded-md bg-white hover:bg-gray-50"
        >
          Clear All
        </Button>
      </div>

      <div
        className={`border border-gray-200 rounded-md ${disabled ? 'bg-gray-50' : 'bg-white'}`}
        style={{ maxHeight, overflowY: "auto", scrollbarWidth: "thin" }}
      >
        <div className="flex items-center space-x-2 py-2 px-3 border-b border-gray-100">
          <input
            type="checkbox"
            id={`select-all-${title.toLowerCase().replace(/\s+/g, '-')}`}
            checked={allSelected}
            onChange={(e) => {
              if (e.target.checked) {
                handleSelectAll();
              } else {
                handleClearAll();
              }
            }}
            className="h-4 w-4 rounded border-gray-300"
            disabled={disabled || options.length === 0}
          />
          <Label
            htmlFor={`select-all-${title.toLowerCase().replace(/\s+/g, '-')}`}
            className={`text-sm font-medium ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
          >
            Select all {title.toLowerCase()}
          </Label>
        </div>

        <div className="p-1">
          {options.length > 0 ? (
            options.map((option) => (
              <div key={option} className="flex items-center space-x-2 py-2 px-2 hover:bg-gray-50">
                <input
                  type="checkbox"
                  id={`${title.toLowerCase().replace(/\s+/g, '-')}-${option.toLowerCase().replace(/\s+/g, '-')}`}
                  checked={selectedValues.includes(option)}
                  onChange={(e) => handleToggleItem(option, e.target.checked)}
                  className="h-4 w-4 rounded border-gray-300"
                  disabled={disabled}
                />
                <Label
                  htmlFor={`${title.toLowerCase().replace(/\s+/g, '-')}-${option.toLowerCase().replace(/\s+/g, '-')}`}
                  className={`text-sm ${disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  {option}
                </Label>
              </div>
            ))
          ) : (
            <div className="text-sm text-muted-foreground py-2 px-2 text-center italic">
              {disabled ? 'Please select options from the previous level first' : 'No options available'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
