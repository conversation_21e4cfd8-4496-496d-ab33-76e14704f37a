
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Kauvery Hospital magenta/purple */
    --primary: 328 67% 39%;
    --primary-foreground: 0 0% 100%;

    /* Kauvery Hospital orange */
    --secondary: 32 93% 54%;
    --secondary-foreground: 0 0% 100%;

    /* Light muted background */
    --muted: 328 20% 95%;
    --muted-foreground: 328 30% 40%;

    /* Light accent based on yellow */
    --accent: 45 100% 50%;
    --accent-foreground: 0 0% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    --radius: 0.5rem;

    --sidebar-background: 328 67% 39%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 32 93% 54%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 328 50% 30%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 328 50% 30%;
    --sidebar-ring: 45 100% 50%;
  }

  .dark {
    --background: 328 30% 12%;
    --foreground: 0 0% 100%;

    --card: 328 30% 15%;
    --card-foreground: 0 0% 100%;

    --popover: 328 30% 15%;
    --popover-foreground: 0 0% 100%;

    /* Kauvery Hospital magenta/purple - brighter for dark mode */
    --primary: 328 67% 55%;
    --primary-foreground: 0 0% 100%;

    /* Kauvery Hospital orange - brighter for dark mode */
    --secondary: 32 93% 65%;
    --secondary-foreground: 0 0% 100%;

    /* Dark muted background - improved contrast */
    --muted: 328 20% 25%;
    --muted-foreground: 328 15% 85%;

    /* Dark accent based on yellow - improved visibility */
    --accent: 45 100% 65%;
    --accent-foreground: 0 0% 10%;

    --destructive: 0 70% 45%;
    --destructive-foreground: 0 0% 100%;

    --border: 328 30% 30%;
    --input: 328 30% 20%;
    --ring: 328 67% 55%;

    --sidebar-background: 328 40% 20%;
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 32 93% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 328 30% 15%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 328 30% 15%;
    --sidebar-ring: 45 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.sidebar-item {
  @apply flex items-center gap-3 px-3 py-2 rounded-lg text-sidebar-foreground transition-colors hover:bg-sidebar-accent/50;
}

.sidebar-item.active {
  @apply bg-sidebar-accent text-sidebar-accent-foreground;
}

.stat-card {
  @apply relative overflow-hidden rounded-lg border bg-card p-5 shadow-sm transition-all hover:shadow-md;
}

.shadow-glow {
  box-shadow: 0 0 8px 2px rgba(var(--primary), 0.3);
}

.stat-count {
  @apply text-3xl font-bold;
}

.tooltip {
  @apply invisible absolute -top-10 left-1/2 -translate-x-1/2 whitespace-nowrap rounded bg-black px-2 py-1 text-white opacity-0 transition-all group-hover:visible group-hover:opacity-100;
}

.logo-container {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm p-2;
}

/* Dark mode text enhancement utilities */
.dark-text-enhanced {
  @apply dark:text-white dark:font-medium;
}

.dark-text-muted {
  @apply dark:text-gray-300;
}

.dark-border-enhanced {
  @apply dark:border-gray-600;
}

.dark-card {
  @apply dark:bg-gray-800/50 dark:backdrop-blur-sm;
}
