import React, { useState } from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";

// Mock data for the filters
const knowledgeAreas = [
  { id: 'area1', name: 'Computer Science' },
  { id: 'area2', name: 'Business Management' },
  { id: 'area3', name: 'Medicine' },
  { id: 'area4', name: 'Engineering' },
  { id: 'area5', name: 'Arts' },
];

const knowledgeTopics = {
  area1: [
    { id: 'topic1', name: 'Programming' },
    { id: 'topic2', name: 'Data Science' },
    { id: 'topic3', name: 'Artificial Intelligence' },
  ],
  area2: [
    { id: 'topic4', name: 'Project Management' },
    { id: 'topic5', name: 'Finance' },
    { id: 'topic6', name: 'Marketing' },
  ],
  area3: [
    { id: 'topic7', name: 'Medical Procedures' },
    { id: 'topic8', name: 'Diagnostics' },
    { id: 'topic9', name: 'Pharmacology' },
  ],
  area4: [
    { id: 'topic10', name: 'Mechanical Engineering' },
    { id: 'topic11', name: 'Electrical Engineering' },
    { id: 'topic12', name: 'Civil Engineering' },
  ],
  area5: [
    { id: 'topic13', name: 'Visual Arts' },
    { id: 'topic14', name: 'Music' },
    { id: 'topic15', name: 'Literature' },
  ],
};

const knowledgeUnits = {
  topic1: [
    { id: 'unit1', name: 'Object-Oriented Programming' },
    { id: 'unit2', name: 'Functional Programming' },
    { id: 'unit3', name: 'Web Development' },
  ],
  topic2: [
    { id: 'unit4', name: 'Data Analysis' },
    { id: 'unit5', name: 'Machine Learning' },
    { id: 'unit6', name: 'Big Data' },
  ],
  // More units for other topics would be defined here
};

interface KnowledgeSelectionFiltersProps {
  onSelectionChange: (selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  }) => void;
}

const KnowledgeSelectionFilters: React.FC<KnowledgeSelectionFiltersProps> = ({
  onSelectionChange,
}) => {
  const [selectedArea, setSelectedArea] = useState<string | null>(null);
  const [selectedTopic, setSelectedTopic] = useState<string | null>(null);
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);

  const handleAreaChange = (value: string) => {
    setSelectedArea(value);
    setSelectedTopic(null);
    setSelectedUnits([]);
    onSelectionChange({ area: value, topic: null, units: [] });
  };

  const handleTopicChange = (value: string) => {
    setSelectedTopic(value);
    setSelectedUnits([]);
    onSelectionChange({ area: selectedArea, topic: value, units: [] });
  };

  const handleUnitChange = (value: string) => {
    const newSelectedUnits = selectedUnits.includes(value)
      ? selectedUnits.filter(unit => unit !== value)
      : [...selectedUnits, value];
    
    setSelectedUnits(newSelectedUnits);
    onSelectionChange({
      area: selectedArea,
      topic: selectedTopic,
      units: newSelectedUnits,
    });
  };

  return (
    <Card>
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Knowledge Area</label>
            <Select onValueChange={handleAreaChange} value={selectedArea || undefined}>
              <SelectTrigger>
                <SelectValue placeholder="Select Knowledge Area" />
              </SelectTrigger>
              <SelectContent>
                {knowledgeAreas.map((area) => (
                  <SelectItem key={area.id} value={area.id}>
                    {area.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Knowledge Topic</label>
            <Select 
              onValueChange={handleTopicChange} 
              value={selectedTopic || undefined}
              disabled={!selectedArea}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Knowledge Topic" />
              </SelectTrigger>
              <SelectContent>
                {selectedArea &&
                  knowledgeTopics[selectedArea as keyof typeof knowledgeTopics]?.map((topic) => (
                    <SelectItem key={topic.id} value={topic.id}>
                      {topic.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium mb-2 block">Knowledge Units</label>
            <Select 
              disabled={!selectedTopic}
              value={selectedUnits.length > 0 ? selectedUnits[0] : undefined}
              onValueChange={handleUnitChange}
            >
              <SelectTrigger>
                <SelectValue 
                  placeholder="Select Knowledge Units" 
                />
              </SelectTrigger>
              <SelectContent>
                {selectedTopic &&
                  knowledgeUnits[selectedTopic as keyof typeof knowledgeUnits]?.map((unit) => (
                    <SelectItem key={unit.id} value={unit.id}>
                      {unit.name}
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
            {selectedUnits.length > 0 && (
              <div className="mt-2">
                <p className="text-sm font-medium mb-1">Selected Units:</p>
                <div className="flex flex-wrap gap-2">
                  {selectedUnits.map(unitId => {
                    const unitName = selectedTopic && 
                      knowledgeUnits[selectedTopic as keyof typeof knowledgeUnits]?.find(
                        unit => unit.id === unitId
                      )?.name;
                    return (
                      <div 
                        key={unitId} 
                        className="bg-primary/10 text-primary text-xs rounded-full px-3 py-1 flex items-center"
                      >
                        {unitName}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default KnowledgeSelectionFilters;