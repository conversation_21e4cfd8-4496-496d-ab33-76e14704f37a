
// Mock data for analytics visualizations
export const performanceData = [
  { month: 'Jan', completion: 65, engagement: 40, assessment: 72 },
  { month: 'Feb', completion: 68, engagement: 45, assessment: 75 },
  { month: 'Mar', completion: 72, engagement: 50, assessment: 78 },
  { month: 'Apr', completion: 78, engagement: 55, assessment: 80 },
  { month: 'May', completion: 82, engagement: 60, assessment: 83 },
  { month: 'Jun', completion: 85, engagement: 65, assessment: 85 },
  { month: 'Jul', completion: 88, engagement: 70, assessment: 88 },
  { month: 'Aug', completion: 90, engagement: 75, assessment: 90 },
];

export const knowledgeAreaCoverage = [
  { name: 'Computer Science', value: 35, color: '#8884d8' },
  { name: 'Business Management', value: 25, color: '#83a6ed' },
  { name: 'Medicine', value: 20, color: '#8dd1e1' },
  { name: 'Engineering', value: 15, color: '#82ca9d' },
  { name: 'Arts', value: 5, color: '#a4de6c' }
];

export const unitCompletionByTopic = [
  { topic: 'Programming', completed: 42, total: 50 },
  { topic: 'Data Science', completed: 38, total: 45 },
  { topic: 'Project Management', completed: 30, total: 40 },
  { topic: 'Finance', completed: 25, total: 35 },
  { topic: 'Medical Procedures', completed: 20, total: 30 },
  { topic: 'Diagnostics', completed: 15, total: 25 },
];

export const skillRadarData = [
  {
    subject: 'Technical',
    user: 80,
    average: 65,
    fullMark: 100,
  },
  {
    subject: 'Management',
    user: 75,
    average: 70,
    fullMark: 100,
  },
  {
    subject: 'Communication',
    user: 85,
    average: 75,
    fullMark: 100,
  },
  {
    subject: 'Problem Solving',
    user: 78,
    average: 68,
    fullMark: 100,
  },
  {
    subject: 'Teamwork',
    user: 90,
    average: 72,
    fullMark: 100,
  },
];

export const weeklyEngagementData = [
  { day: 'Mon', hours: 2.5 },
  { day: 'Tue', hours: 3.8 },
  { day: 'Wed', hours: 2.1 },
  { day: 'Thu', hours: 4.2 },
  { day: 'Fri', hours: 3.5 },
  { day: 'Sat', hours: 1.2 },
  { day: 'Sun', hours: 0.8 },
];

export const userProgressConfig = {
  completion: {
    label: "Completion",
    theme: {
      light: "#8884d8",
      dark: "#8884d8"
    }
  },
  engagement: {
    label: "Engagement",
    theme: {
      light: "#82ca9d",
      dark: "#82ca9d"
    }
  },
  assessment: {
    label: "Assessment",
    theme: {
      light: "#ffc658",
      dark: "#ffc658"
    }
  }
};

export const radarConfig = {
  user: {
    label: "Your Skills",
    theme: {
      light: "#8884d8",
      dark: "#8884d8"
    }
  },
  average: {
    label: "Team Average",
    theme: {
      light: "#82ca9d", 
      dark: "#82ca9d"
    }
  }
};

