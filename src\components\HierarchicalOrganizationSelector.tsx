import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { OrganizationCheckboxList } from "@/components/OrganizationCheckboxList";
import { OrganizationTable } from "@/components/OrganizationTable";
import { businessUnits, departmentGroups, departments, divisions, subDivisions, categories, grades, designations } from "@/data/organizationData";

interface HierarchicalOrganizationSelectorProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  };
  onSelectionChange: (selection: {
    businessUnits: string[];
    departmentGroups: string[];
    departments: string[];
    divisions: string[];
    subDivisions: string[];
    categories: string[];
    grades: string[];
    designations: string[];
  }) => void;
  onConfirm: () => void;
}

export function HierarchicalOrganizationSelector({
  open,
  onOpenChange,
  selection,
  onSelectionChange,
  onConfirm,
}: HierarchicalOrganizationSelectorProps) {
  // Local state for selection during editing
  const [localSelection, setLocalSelection] = useState({
    businessUnits: [] as string[],
    departmentGroups: [] as string[],
    departments: [] as string[],
    divisions: [] as string[],
    subDivisions: [] as string[],
    categories: [] as string[],
    grades: [] as string[],
    designations: [] as string[],
  });

  // Available options based on hierarchy
  const [availableDepartmentGroups, setAvailableDepartmentGroups] = useState<string[]>([]);
  const [availableDepartments, setAvailableDepartments] = useState<string[]>([]);
  const [availableDivisions, setAvailableDivisions] = useState<string[]>([]);
  const [availableSubDivisions, setAvailableSubDivisions] = useState<string[]>([]);
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableGrades, setAvailableGrades] = useState<string[]>([]);
  const [availableDesignations, setAvailableDesignations] = useState<string[]>([]);

  // Initialize local selection when dialog opens
  useEffect(() => {
    if (open) {
      setLocalSelection(selection);
    }
  }, [open, selection]);

  // Update available department groups when business units change
  useEffect(() => {
    if (localSelection.businessUnits.length > 0) {
      const availableDGs = new Set<string>();
      localSelection.businessUnits.forEach(bu => {
        if (departmentGroups[bu]) {
          departmentGroups[bu].forEach(dg => availableDGs.add(dg));
        }
      });
      setAvailableDepartmentGroups(Array.from(availableDGs));

      // Filter out department groups that are no longer available
      setLocalSelection(prev => ({
        ...prev,
        departmentGroups: prev.departmentGroups.filter(dg => availableDGs.has(dg))
      }));
    } else {
      setAvailableDepartmentGroups([]);
      setLocalSelection(prev => ({
        ...prev,
        departmentGroups: [],
        departments: [],
        divisions: [],
        subDivisions: [],
        categories: [],
        grades: [],
        designations: []
      }));
    }
  }, [localSelection.businessUnits]);

  // Update available departments when department groups change
  useEffect(() => {
    if (localSelection.departmentGroups.length > 0) {
      const availableDepts = new Set<string>();
      localSelection.departmentGroups.forEach(dg => {
        if (departments[dg]) {
          departments[dg].forEach(dept => availableDepts.add(dept));
        }
      });
      setAvailableDepartments(Array.from(availableDepts));

      // Filter out departments that are no longer available
      setLocalSelection(prev => ({
        ...prev,
        departments: prev.departments.filter(dept => availableDepts.has(dept))
      }));
    } else {
      setAvailableDepartments([]);
      setLocalSelection(prev => ({
        ...prev,
        departments: [],
        divisions: [],
        subDivisions: [],
        categories: [],
        grades: [],
        designations: []
      }));
    }
  }, [localSelection.departmentGroups]);

  // Update available divisions when departments change
  useEffect(() => {
    if (localSelection.departments.length > 0) {
      const availableDiv = new Set<string>();
      localSelection.departments.forEach(dept => {
        if (divisions[dept]) {
          divisions[dept].forEach(div => availableDiv.add(div));
        }
      });
      setAvailableDivisions(Array.from(availableDiv));

      // Filter out divisions that are no longer available
      setLocalSelection(prev => ({
        ...prev,
        divisions: prev.divisions.filter(div => availableDiv.has(div))
      }));
    } else {
      setAvailableDivisions([]);
      setLocalSelection(prev => ({
        ...prev,
        divisions: [],
        subDivisions: [],
        categories: [],
        grades: [],
        designations: []
      }));
    }
  }, [localSelection.departments]);

  // Update available sub-divisions when divisions change
  useEffect(() => {
    if (localSelection.divisions.length > 0) {
      const availableSubDiv = new Set<string>();
      localSelection.divisions.forEach(div => {
        if (subDivisions[div]) {
          subDivisions[div].forEach(subDiv => availableSubDiv.add(subDiv));
        }
      });
      setAvailableSubDivisions(Array.from(availableSubDiv));

      // Filter out sub-divisions that are no longer available
      setLocalSelection(prev => ({
        ...prev,
        subDivisions: prev.subDivisions.filter(subDiv => availableSubDiv.has(subDiv))
      }));
    } else {
      setAvailableSubDivisions([]);
      setLocalSelection(prev => ({
        ...prev,
        subDivisions: [],
        categories: [],
        grades: [],
        designations: []
      }));
    }
  }, [localSelection.divisions]);

  // Update available categories when sub-divisions change
  useEffect(() => {
    if (localSelection.subDivisions.length > 0) {
      const availableCat = new Set<string>();
      localSelection.subDivisions.forEach(subDiv => {
        if (categories[subDiv]) {
          categories[subDiv].forEach(cat => availableCat.add(cat));
        }
      });
      setAvailableCategories(Array.from(availableCat));

      // Filter out categories that are no longer available
      setLocalSelection(prev => ({
        ...prev,
        categories: prev.categories.filter(cat => availableCat.has(cat))
      }));
    } else {
      setAvailableCategories([]);
      setLocalSelection(prev => ({
        ...prev,
        categories: [],
        grades: [],
        designations: []
      }));
    }
  }, [localSelection.subDivisions]);

  // Update available grades when categories change
  useEffect(() => {
    if (localSelection.categories.length > 0) {
      const availableGrd = new Set<string>();
      localSelection.categories.forEach(cat => {
        if (grades[cat]) {
          grades[cat].forEach(grd => availableGrd.add(grd));
        }
      });
      setAvailableGrades(Array.from(availableGrd));

      // Filter out grades that are no longer available
      setLocalSelection(prev => ({
        ...prev,
        grades: prev.grades.filter(grd => availableGrd.has(grd))
      }));
    } else {
      setAvailableGrades([]);
      setLocalSelection(prev => ({
        ...prev,
        grades: [],
        designations: []
      }));
    }
  }, [localSelection.categories]);

  // Update available designations when grades change
  useEffect(() => {
    if (localSelection.grades.length > 0) {
      const availableDesig = new Set<string>();
      localSelection.grades.forEach(grd => {
        if (designations[grd]) {
          designations[grd].forEach(desig => availableDesig.add(desig));
        }
      });
      setAvailableDesignations(Array.from(availableDesig));

      // Filter out designations that are no longer available
      setLocalSelection(prev => ({
        ...prev,
        designations: prev.designations.filter(desig => availableDesig.has(desig))
      }));
    } else {
      setAvailableDesignations([]);
      setLocalSelection(prev => ({
        ...prev,
        designations: []
      }));
    }
  }, [localSelection.grades]);

  // Handle selection changes
  const handleBusinessUnitChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, businessUnits: values }));
  };

  const handleDepartmentGroupChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, departmentGroups: values }));
  };

  const handleDepartmentChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, departments: values }));
  };

  const handleDivisionChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, divisions: values }));
  };

  const handleSubDivisionChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, subDivisions: values }));
  };

  const handleCategoryChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, categories: values }));
  };

  const handleGradeChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, grades: values }));
  };

  const handleDesignationChange = (values: string[]) => {
    setLocalSelection(prev => ({ ...prev, designations: values }));
  };

  // Handle confirm button click
  const handleConfirm = () => {
    onSelectionChange(localSelection);
    onConfirm();
    onOpenChange(false);
  };

  // Check if any selections have been made
  const hasSelections =
    localSelection.businessUnits.length > 0 ||
    localSelection.departmentGroups.length > 0 ||
    localSelection.departments.length > 0 ||
    localSelection.divisions.length > 0 ||
    localSelection.subDivisions.length > 0 ||
    localSelection.categories.length > 0 ||
    localSelection.grades.length > 0 ||
    localSelection.designations.length > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl h-[85vh] flex flex-col p-6 overflow-hidden">
        <DialogHeader className="px-0 pb-4 flex-shrink-0">
          <DialogTitle className="text-xl font-semibold">Select Organization</DialogTitle>
          <p className="text-sm text-muted-foreground mt-2">
            Select options in order: Business Unit → Department Group → Department → Division → Sub-Division → Category → Grade → Designation
          </p>
        </DialogHeader>

        <ScrollArea className="flex-grow pr-4 overflow-y-auto">
          <div className="space-y-6">
            {/* Level 1: Business Units */}
            <div>
              <OrganizationCheckboxList
                title="Business Units"
                options={businessUnits}
                selectedValues={localSelection.businessUnits}
                onChange={handleBusinessUnitChange}
                maxHeight="150px"
              />
            </div>

            {/* Level 2: Department Groups */}
            <div>
              <OrganizationCheckboxList
                title="Department Groups"
                options={availableDepartmentGroups}
                selectedValues={localSelection.departmentGroups}
                onChange={handleDepartmentGroupChange}
                maxHeight="150px"
                disabled={localSelection.businessUnits.length === 0}
              />
            </div>

            {/* Level 3: Department */}
            <div>
              <OrganizationCheckboxList
                title="Department"
                options={availableDepartments}
                selectedValues={localSelection.departments}
                onChange={handleDepartmentChange}
                maxHeight="150px"
                disabled={availableDepartmentGroups.length === 0 || localSelection.departmentGroups.length === 0}
              />
            </div>

            {/* Level 4: Division */}
            <div>
              <OrganizationCheckboxList
                title="Division"
                options={availableDivisions}
                selectedValues={localSelection.divisions}
                onChange={handleDivisionChange}
                maxHeight="150px"
                disabled={availableDepartments.length === 0 || localSelection.departments.length === 0}
              />
            </div>

            {/* Level 5: Sub-Division */}
            <div>
              <OrganizationCheckboxList
                title="Sub-Division"
                options={availableSubDivisions}
                selectedValues={localSelection.subDivisions}
                onChange={handleSubDivisionChange}
                maxHeight="150px"
                disabled={availableDivisions.length === 0 || localSelection.divisions.length === 0}
              />
            </div>

            {/* Level 6: Category */}
            <div>
              <OrganizationCheckboxList
                title="Category"
                options={availableCategories}
                selectedValues={localSelection.categories}
                onChange={handleCategoryChange}
                maxHeight="150px"
                disabled={availableSubDivisions.length === 0 || localSelection.subDivisions.length === 0}
              />
            </div>

            {/* Level 7: Grade */}
            <div>
              <OrganizationCheckboxList
                title="Grade"
                options={availableGrades}
                selectedValues={localSelection.grades}
                onChange={handleGradeChange}
                maxHeight="150px"
                disabled={availableCategories.length === 0 || localSelection.categories.length === 0}
              />
            </div>

            {/* Level 8: Designation */}
            <div>
              <OrganizationCheckboxList
                title="Designation"
                options={availableDesignations}
                selectedValues={localSelection.designations}
                onChange={handleDesignationChange}
                maxHeight="150px"
                disabled={availableGrades.length === 0 || localSelection.grades.length === 0}
              />
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className="mt-4 flex justify-end space-x-2 px-0 flex-shrink-0">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="rounded-md px-4 py-2 h-10 bg-white hover:bg-gray-50"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="bg-[#B71261] hover:bg-[#A01050] text-white rounded-md px-4 py-2 h-10"
          >
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
