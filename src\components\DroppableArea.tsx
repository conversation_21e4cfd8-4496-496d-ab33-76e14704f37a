import React, { useState } from "react";
import { ContentComponent, DroppedItem, ContentMode } from "@/types";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import ComponentRenderer from "./ComponentRenderer";
import { ScrollArea } from "@/components/ui/scroll-area";
import AddComponentDialog from "./AddComponentDialog";
import WorkspaceHeader from "./WorkspaceHeader";
import MobilePreview from "./MobilePreview";
import { toast } from "@/hooks/use-toast";

interface DroppableAreaProps {
  activeMode: ContentMode;
  items: DroppedItem[];
  onAddItem: (item: DroppedItem) => void;
  onRemoveItem: (id: string) => void;
  onUpdateItem: (id: string, data: ContentComponent) => void;
  onReorderItems?: (sourceId: string, targetId: string) => void;
}

const DroppableArea: React.FC<DroppableAreaProps> = ({
  activeMode,
  items,
  onAddItem,
  onRemoveItem,
  onUpdateItem,
  onReorderItems,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isDragOver) setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const componentType = e.dataTransfer.getData("componentType");
    if (!componentType) return;

    handleAddComponent(componentType);
  };

  const handleAddComponent = (componentType: string) => {
    const newItemId = uuidv4();
    const position = items.length;

    // Create appropriate initial structure based on component type
    let newComponent: ContentComponent;

    switch (componentType) {
      case 'mcq':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          question: '',
          options: [
            { id: uuidv4(), text: 'Option 1' },
            { id: uuidv4(), text: 'Option 2' }
          ],
          allowMultiple: false,
          required: true
        };
        break;

      case 'option':
      case 'dropdown':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: `New ${componentType} field`,
          options: [
            { id: uuidv4(), text: 'Option 1' },
            { id: uuidv4(), text: 'Option 2' }
          ],
          required: true
        };
        break;

      case 'checkbox':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: `New ${componentType} field`,
          options: [
            { id: uuidv4(), text: 'Yes' },
            { id: uuidv4(), text: 'No' },
            { id: uuidv4(), text: 'Not Applicable' }
          ],
          required: true
        };
        break;

      case 'star':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Rating',
          maxStars: 5,
          required: true
        };
        break;

      case 'time':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Time',
          hasRange: false,
          required: true
        };
        break;

      case 'alphanumeric':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Alphanumeric',
          containNumber: false,
          containLetters: false,
          required: true
        };
        break;

      case 'date':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Date',
          required: true
        };
        break;

      case 'duration':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Duration',
          required: true
        };
        break;

      case 'phone':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Phone',
          required: true
        };
        break;

      case 'sign':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Signature',
          required: true
        };
        break;

      case 'textbox':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: 'Text Input',
          required: true
        };
        break;

      case 'feedback-image':
      case 'feedback-video':
      case 'feedback-audio':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          label: `Upload ${componentType.replace('feedback-', '')}`,
          required: true,
          allowFromLibrary: true
        };
        break;

      case 'scorm':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          title: 'SCORM Package',
          required: false,
        };
        break;

      case 'webgl':
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          title: 'WebGL Content',
          required: false,
        };
        break;

      default:
        // Generic component creation for other types
        newComponent = {
          id: newItemId,
          type: componentType,
          position,
          required: activeMode === "feedback",
          allowFromLibrary: activeMode === "feedback" &&
            ["feedback-image", "feedback-video", "feedback-audio"].includes(componentType),
        } as ContentComponent;
    }

    const newItem: DroppedItem = {
      id: newItemId,
      type: componentType,
      data: newComponent,
    };

    onAddItem(newItem);
  };

  const handleSave = () => {
    // In a real application, this would save to a database or API
    // For now, we'll just show a success message
    toast({
      title: "Content Saved",
      description: `${items.length} components saved successfully.`,
      variant: "default",
    });
  };

  const handlePreview = () => {
    setPreviewOpen(true);
  };

  return (
    <div className="flex-1 flex flex-col overflow-hidden bg-white dark:bg-slate-900">
      <WorkspaceHeader
        items={items}
        onSave={handleSave}
        onPreview={handlePreview}
      />

      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 flex items-center justify-between">
          <div>
            <h2 className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Workspace
            </h2>
            <p className="text-xs text-muted-foreground">
              {items.length} {items.length === 1 ? 'component' : 'components'} added
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1 h-8"
            onClick={() => setDialogOpen(true)}
          >
            <Plus className="h-3.5 w-3.5" />
            Add Component
          </Button>
        </div>

        <div className="flex-1 overflow-hidden p-6">
          <div
            className={`droppable-area rounded-lg border-2 h-full bg-white dark:bg-slate-800 shadow-sm transition-all duration-300 ${
              isDragOver ? "border-primary border-dashed scale-[0.99] bg-primary/5" : "border-slate-200 dark:border-slate-700"
            } ${items.length === 0 ? "flex items-center justify-center" : "overflow-hidden"}`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            {items.length === 0 ? (
              <div className="text-center p-10 text-muted-foreground max-w-md mx-auto">
                <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-slate-100 dark:bg-slate-700 flex items-center justify-center">
                  <Plus className="h-10 w-10 text-primary opacity-70" />
                </div>
                <h3 className="text-xl font-medium mb-2 text-slate-700 dark:text-slate-300">Your canvas is empty</h3>
                <p className="mb-1">Drag components from the sidebar to start building</p>
                <p className="text-sm mb-4">or</p>
                <Button
                  variant="default"
                  className="mt-2 px-6 py-5 h-auto text-md font-medium shadow-md hover:shadow-lg transition-all duration-200"
                  onClick={() => setDialogOpen(true)}
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Add Component
                </Button>
              </div>
            ) : (
              <ScrollArea className="h-full">
                <div className="space-y-5 p-6">
                  {items.map((item, index) => (
                    <ComponentRenderer
                      key={item.id}
                      item={item}
                      index={index}
                      onRemove={onRemoveItem}
                      onUpdate={onUpdateItem}
                      onReorder={onReorderItems}
                      isDraggable={items.length > 1}
                    />
                  ))}
                  <Button
                    variant="outline"
                    className="w-full mt-6 border-dashed border-2 py-6 h-auto hover:border-primary hover:bg-primary/5 transition-all duration-200"
                    onClick={() => setDialogOpen(true)}
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Add Component
                  </Button>
                </div>
              </ScrollArea>
            )}
          </div>
        </div>
      </div>

      <AddComponentDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSelectComponent={handleAddComponent}
        activeMode={activeMode}
      />

      <MobilePreview
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        items={items}
      />
    </div>
  );
};

export default DroppableArea;
