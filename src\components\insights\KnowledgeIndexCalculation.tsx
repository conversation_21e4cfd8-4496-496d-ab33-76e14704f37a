import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { calculateKnowledgeIndex } from "@/data/insightsData";
import { motion } from 'framer-motion';
import { AlertTriangle, CheckCircle, AlertCircle, Info, TrendingUp } from 'lucide-react';
import { Progress } from "@/components/ui/progress";

interface KnowledgeIndexCalculationProps {
  selection: {
    area: string | null;
    topic: string | null;
    units: string[];
  };
}

const KnowledgeIndexCalculation: React.FC<KnowledgeIndexCalculationProps> = ({ selection }) => {
  const { lowRisk, mediumRisk, risk, highRisk, pullRate } = calculateKnowledgeIndex(selection);

  // Animation states for counters
  const [animatedLowRisk, setAnimatedLowRisk] = useState(0);
  const [animatedMediumRisk, setAnimatedMediumRisk] = useState(0);
  const [animatedRisk, setAnimatedRisk] = useState(0);
  const [animatedHighRisk, setAnimatedHighRisk] = useState(0);
  const [animatedPullRate, setAnimatedPullRate] = useState(0);

  // Animate the values when they change
  useEffect(() => {
    const duration = 1000; // Animation duration in ms
    const steps = 20; // Number of steps in the animation
    const stepTime = duration / steps;

    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;

      setAnimatedLowRisk(Math.round(lowRisk * progress));
      setAnimatedMediumRisk(Math.round(mediumRisk * progress));
      setAnimatedRisk(Math.round(risk * progress));
      setAnimatedHighRisk(Math.round(highRisk * progress));
      setAnimatedPullRate(Math.round(pullRate * progress));

      if (currentStep >= steps) {
        clearInterval(timer);
        setAnimatedLowRisk(lowRisk);
        setAnimatedMediumRisk(mediumRisk);
        setAnimatedRisk(risk);
        setAnimatedHighRisk(highRisk);
        setAnimatedPullRate(pullRate);
      }
    }, stepTime);

    return () => clearInterval(timer);
  }, [lowRisk, mediumRisk, risk, highRisk, pullRate]);

  // Calculate total for the progress bars
  const total = lowRisk + mediumRisk + risk + highRisk;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-500" />
            Knowledge Index Calculation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <motion.div
              className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-5 shadow-sm border border-green-200 relative overflow-hidden"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-sm font-medium text-green-700">Low Risk</h3>
                <CheckCircle className="h-5 w-5 text-green-500" />
              </div>
              <p className="text-3xl font-bold text-green-800">{animatedLowRisk}%</p>
              <div className="mt-3">
                <Progress value={lowRisk} className="h-1.5 bg-green-200" indicatorClassName="bg-green-500" />
              </div>
              <motion.div
                className="absolute -bottom-6 -right-6 w-24 h-24 bg-green-200 rounded-full opacity-20"
                animate={{ scale: [1, 1.1, 1], opacity: [0.2, 0.3, 0.2] }}
                transition={{ duration: 3, repeat: Infinity }}
              />
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-5 shadow-sm border border-blue-200 relative overflow-hidden"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-sm font-medium text-blue-700">Medium Risk</h3>
                <Info className="h-5 w-5 text-blue-500" />
              </div>
              <p className="text-3xl font-bold text-blue-800">{animatedMediumRisk}%</p>
              <div className="mt-3">
                <Progress value={mediumRisk} className="h-1.5 bg-blue-200" indicatorClassName="bg-blue-500" />
              </div>
              <motion.div
                className="absolute -bottom-6 -right-6 w-24 h-24 bg-blue-200 rounded-full opacity-20"
                animate={{ scale: [1, 1.1, 1], opacity: [0.2, 0.3, 0.2] }}
                transition={{ duration: 3, repeat: Infinity, delay: 0.5 }}
              />
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-5 shadow-sm border border-yellow-200 relative overflow-hidden"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.3 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-sm font-medium text-yellow-700">Risk</h3>
                <AlertCircle className="h-5 w-5 text-yellow-500" />
              </div>
              <p className="text-3xl font-bold text-yellow-800">{animatedRisk}%</p>
              <div className="mt-3">
                <Progress value={risk} className="h-1.5 bg-yellow-200" indicatorClassName="bg-yellow-500" />
              </div>
              <motion.div
                className="absolute -bottom-6 -right-6 w-24 h-24 bg-yellow-200 rounded-full opacity-20"
                animate={{ scale: [1, 1.1, 1], opacity: [0.2, 0.3, 0.2] }}
                transition={{ duration: 3, repeat: Infinity, delay: 1 }}
              />
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-5 shadow-sm border border-red-200 relative overflow-hidden"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.4 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-sm font-medium text-red-700">High Risk</h3>
                <AlertTriangle className="h-5 w-5 text-red-500" />
              </div>
              <p className="text-3xl font-bold text-red-800">{animatedHighRisk}%</p>
              <div className="mt-3">
                <Progress value={highRisk} className="h-1.5 bg-red-200" indicatorClassName="bg-red-500" />
              </div>
              <motion.div
                className="absolute -bottom-6 -right-6 w-24 h-24 bg-red-200 rounded-full opacity-20"
                animate={{ scale: [1, 1.1, 1], opacity: [0.2, 0.3, 0.2] }}
                transition={{ duration: 3, repeat: Infinity, delay: 1.5 }}
              />
            </motion.div>

            <motion.div
              className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-5 shadow-sm border border-purple-200 relative overflow-hidden"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.5 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-sm font-medium text-purple-700">Pull Rate</h3>
                <TrendingUp className="h-5 w-5 text-purple-500" />
              </div>
              <p className="text-3xl font-bold text-purple-800">{animatedPullRate}%</p>
              <div className="mt-3">
                <Progress value={pullRate} max={100} className="h-1.5 bg-purple-200" indicatorClassName="bg-purple-500" />
              </div>
              <motion.div
                className="absolute -bottom-6 -right-6 w-24 h-24 bg-purple-200 rounded-full opacity-20"
                animate={{ scale: [1, 1.1, 1], opacity: [0.2, 0.3, 0.2] }}
                transition={{ duration: 3, repeat: Infinity, delay: 2 }}
              />
            </motion.div>
          </div>

          {/* Overall Risk Distribution */}
          <motion.div
            className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <h3 className="text-sm font-medium text-gray-700 mb-3">Overall Risk Distribution</h3>
            <div className="h-8 flex rounded-md overflow-hidden">
              <motion.div
                className="bg-green-500 h-full"
                initial={{ width: 0 }}
                animate={{ width: `${lowRisk}%` }}
                transition={{ duration: 1, delay: 0.7 }}
              />
              <motion.div
                className="bg-blue-500 h-full"
                initial={{ width: 0 }}
                animate={{ width: `${mediumRisk}%` }}
                transition={{ duration: 1, delay: 0.8 }}
              />
              <motion.div
                className="bg-yellow-500 h-full"
                initial={{ width: 0 }}
                animate={{ width: `${risk}%` }}
                transition={{ duration: 1, delay: 0.9 }}
              />
              <motion.div
                className="bg-red-500 h-full"
                initial={{ width: 0 }}
                animate={{ width: `${highRisk}%` }}
                transition={{ duration: 1, delay: 1 }}
              />
            </div>
            <div className="flex justify-between mt-2 text-xs text-gray-600">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded-sm mr-1"></div>
                <span>Low Risk ({lowRisk}%)</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-500 rounded-sm mr-1"></div>
                <span>Medium Risk ({mediumRisk}%)</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-yellow-500 rounded-sm mr-1"></div>
                <span>Risk ({risk}%)</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded-sm mr-1"></div>
                <span>High Risk ({highRisk}%)</span>
              </div>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default KnowledgeIndexCalculation;