
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Calendar, BarChart3, <PERSON><PERSON><PERSON>, LineChart, Activity, Users, BookOpen, GraduationCap, Brain, Target, Award } from "lucide-react";
import { motion } from "framer-motion";

// Import custom components
import KnowledgeIndexDonut from "@/components/dashboard/KnowledgeIndexDonut";
import DashboardHeader from "@/components/dashboard/DashboardHeader";
import StatsGrid from "@/components/dashboard/StatsGrid";
import AnimatedBar<PERSON><PERSON> from "@/components/dashboard/AnimatedBarChart";
import Animated<PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/dashboard/AnimatedRadarChart";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "@/components/dashboard/AnimatedLineChart";
import Animated<PERSON><PERSON><PERSON><PERSON> from "@/components/dashboard/AnimatedPieChart";

export default function Dashboard() {
  const [refreshKey, setRefreshKey] = useState(0);

  // Handler for refreshing data
  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  // Handler for filter changes
  const handleFilterChange = (period: string) => {
    console.log(`Filter changed to: ${period}`);
    // In a real app, you would fetch new data based on the period
  };

  // Stats data
  const statsData = [
    {
      title: "Knowledge Topics",
      count: 25,
      icon: BookOpen,
      className: "bg-gradient-to-br from-blue-500 to-blue-600 text-white",
      iconColor: "text-white",
      tooltipText: "Total knowledge topics",
      trend: 12,
      trendLabel: "from last month"
    },
    {
      title: "Knowledge Units",
      count: 145,
      icon: GraduationCap,
      className: "bg-gradient-to-br from-purple-500 to-purple-600 text-white",
      iconColor: "text-white",
      tooltipText: "Total knowledge units",
      trend: 8,
      trendLabel: "from last month"
    },
    {
      title: "Knowledge Index",
      count: "76%",
      icon: Brain,
      className: "bg-gradient-to-br from-green-500 to-green-600 text-white",
      iconColor: "text-white",
      tooltipText: "Overall knowledge index",
      trend: 5,
      trendLabel: "from last month"
    },
    {
      title: "Active Users",
      count: 1254,
      icon: Users,
      className: "bg-gradient-to-br from-amber-500 to-amber-600 text-white",
      iconColor: "text-white",
      tooltipText: "Users active this month",
      trend: -3,
      trendLabel: "from last month"
    }
  ];

  // Secondary stats data
  const secondaryStatsData = [
    {
      title: "Completion Rate",
      count: "68%",
      icon: Target,
      className: "border border-gray-200",
      iconColor: "text-blue-500 bg-blue-50 p-2 rounded-full",
      trend: 4
    },
    {
      title: "Avg. Score",
      count: 82,
      icon: Award,
      className: "border border-gray-200",
      iconColor: "text-purple-500 bg-purple-50 p-2 rounded-full",
      trend: 2
    },
    {
      title: "Total Courses",
      count: 48,
      icon: BookOpen,
      className: "border border-gray-200",
      iconColor: "text-green-500 bg-green-50 p-2 rounded-full",
      trend: 15
    },
    {
      title: "Certifications",
      count: 356,
      icon: GraduationCap,
      className: "border border-gray-200",
      iconColor: "text-amber-500 bg-amber-50 p-2 rounded-full",
      trend: 7
    }
  ];

  // Knowledge areas data
  const knowledgeAreasData = [
    { name: 'Programming', value: 170, color: '#3B82F6' },
    { name: 'Design', value: 220, color: '#8B5CF6' },
    { name: 'Marketing', value: 250, color: '#10B981' },
    { name: 'Management', value: 275, color: '#F59E0B' },
    { name: 'Communication', value: 260, color: '#EF4444' },
  ];

  // Skill assessment data
  const skillAssessmentData = [
    { subject: 'Analysis', user: 80, average: 65, fullMark: 100 },
    { subject: 'Development', user: 75, average: 60, fullMark: 100 },
    { subject: 'Maintenance', user: 85, average: 70, fullMark: 100 },
    { subject: 'Testing', user: 65, average: 55, fullMark: 100 },
    { subject: 'Design', user: 70, average: 60, fullMark: 100 },
    { subject: 'Documentation', user: 78, average: 62, fullMark: 100 },
  ];

  // Monthly performance data
  const monthlyPerformanceData = [
    { month: "Jan", current: 600, previous: 400 },
    { month: "Feb", current: 700, previous: 500 },
    { month: "Mar", current: 650, previous: 700 },
    { month: "Apr", current: 800, previous: 600 },
    { month: "May", current: 550, previous: 500 },
    { month: "Jun", current: 580, previous: 650 },
    { month: "Jul", current: 700, previous: 550 },
    { month: "Aug", current: 900, previous: 750 },
    { month: "Sep", current: 750, previous: 650 },
    { month: "Oct", current: 830, previous: 500 },
    { month: "Nov", current: 720, previous: 600 },
    { month: "Dec", current: 900, previous: 700 }
  ];

  // User activity data
  const userActivityData = [
    { day: "Mon", active: 340, engaged: 240 },
    { day: "Tue", active: 380, engaged: 280 },
    { day: "Wed", active: 450, engaged: 320 },
    { day: "Thu", active: 520, engaged: 390 },
    { day: "Fri", active: 490, engaged: 350 },
    { day: "Sat", active: 300, engaged: 210 },
    { day: "Sun", active: 280, engaged: 190 }
  ];

  // Knowledge distribution data
  const knowledgeDistributionData = [
    { name: "Beginner", value: 25, color: "#EF4444" },
    { name: "Intermediate", value: 45, color: "#F59E0B" },
    { name: "Advanced", value: 20, color: "#10B981" },
    { name: "Expert", value: 10, color: "#3B82F6" }
  ];

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div
      className="space-y-8"
      key={refreshKey}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      {/* Dashboard Header */}
      <DashboardHeader
        title="Dashboard"
        subtitle="Overview of your knowledge management system"
        onRefresh={handleRefresh}
        onFilterChange={handleFilterChange}
      />

      {/* Main Stats */}
      <StatsGrid stats={statsData} />

      {/* Main Charts */}
      <motion.div
        className="grid grid-cols-1 gap-8 md:grid-cols-2"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Knowledge Index */}
        <motion.div variants={itemVariants}>
          <Card className="overflow-hidden">
            <CardHeader>
              <CardTitle>Knowledge Index</CardTitle>
            </CardHeader>
            <CardContent className="flex items-center justify-center py-6">
              <KnowledgeIndexDonut percentage={76} />
            </CardContent>
          </Card>
        </motion.div>

        {/* Knowledge Areas Chart */}
        <motion.div variants={itemVariants}>
          <AnimatedBarChart
            title="Knowledge Areas"
            data={knowledgeAreasData}
            dataKey="value"
            xAxisKey="name"
            color="#3B82F6"
            height={350}
            showLegend={true}
            legendName="Score"
          />
        </motion.div>
      </motion.div>

      {/* Secondary Stats */}
      <StatsGrid stats={secondaryStatsData} />

      {/* Skill Assessment Radar */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <AnimatedRadarChart
            title="Skill Assessment"
            data={skillAssessmentData}
            dataKey="user"
            color="#8B5CF6"
            height={400}
            showLegend={true}
            legendName="Your Skills"
          />
        </motion.div>
      </motion.div>

      {/* Performance Charts */}
      <motion.div
        className="grid grid-cols-1 gap-8 lg:grid-cols-2"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <AnimatedLineChart
            title="Monthly Performance"
            data={monthlyPerformanceData}
            xAxisKey="month"
            series={[
              { name: "Current", dataKey: "current", color: "#3B82F6" },
              { name: "Previous", dataKey: "previous", color: "#EF4444" }
            ]}
            height={350}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <AnimatedBarChart
            title="Weekly User Activity"
            data={userActivityData}
            dataKey="active"
            xAxisKey="day"
            color="#10B981"
            height={350}
            showLegend={true}
            legendName="Active Users"
          />
        </motion.div>
      </motion.div>

      {/* Knowledge Distribution */}
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 gap-8 lg:grid-cols-2"
      >
        <motion.div variants={itemVariants} className="lg:col-span-2">
          <AnimatedPieChart
            title="Knowledge Distribution"
            data={knowledgeDistributionData}
            dataKey="value"
            nameKey="name"
            height={400}
            innerRadius={100}
            outerRadius={160}
          />
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
