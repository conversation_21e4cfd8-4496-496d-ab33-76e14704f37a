
import React from "react";
import {
  Image,
  FileVideo,
  Video,
  Link,
  FileText,
  FileAudio,
  FileArchive,
  Code,
  Archive,
  Box,
  ListChecks,
  FormInput,
  Upload,
  FileQuestion,
  ChevronDown,
  PenTool,
  CheckSquare,
  Star,
  Calendar,
  Clock,
  Timer,
  Phone,
  Hash,
  Plus
} from "lucide-react";
import { motion } from "framer-motion";
import { DraggableComponent } from "./DraggableComponent";
import { Button } from "@/components/ui/button";

interface ContentComponentsListProps {
  type: "content" | "form";
  isCompact?: boolean;
  onAddComponent?: (component: ComponentItem) => void;
}

export interface ComponentItem {
  id?: string;
  type?: string;
  icon: React.ReactNode;
  title: string;
  description: string;
}

export default function ContentComponentsList({ type, isCompact = false, onAddComponent }: ContentComponentsListProps) {
  const contentComponents: ComponentItem[] = [
    { icon: <Image className="h-5 w-5 text-indigo-600" />, title: "Image", description: "Upload from library" },
    { icon: <FileVideo className="h-5 w-5 text-indigo-600" />, title: "Video", description: "Upload from library" },
    { icon: <Video className="h-5 w-5 text-red-600" />, title: "YouTube", description: "Embed a video" },
    { icon: <Link className="h-5 w-5 text-blue-600" />, title: "Web Link", description: "Add website link" },
    { icon: <FileText className="h-5 w-5 text-green-600" />, title: "Text", description: "Rich text editor" },
    { icon: <FileAudio className="h-5 w-5 text-purple-600" />, title: "Audio", description: "Upload or record" },
    { icon: <FileArchive className="h-5 w-5 text-orange-600" />, title: "Attachment", description: "Upload any file" },
    { icon: <Code className="h-5 w-5 text-gray-600" />, title: "Embed Code", description: "Paste embed code" },
    { icon: <Archive className="h-5 w-5 text-blue-600" />, title: "Scorm", description: "Upload .zip files" },
    { icon: <Box className="h-5 w-5 text-indigo-600" />, title: "WebGL", description: "Upload WebGL files" },
  ];

  const formComponents: ComponentItem[] = [
    { icon: <ListChecks className="h-5 w-5 text-indigo-600" />, title: "MCQ", description: "Multiple choice question" },
    { icon: <FormInput className="h-5 w-5 text-blue-600" />, title: "Textbox", description: "Free text input" },
    { icon: <Upload className="h-5 w-5 text-purple-600" />, title: "Image Upload", description: "With text field" },
    { icon: <FileVideo className="h-5 w-5 text-red-600" />, title: "Video Upload", description: "With text field" },
    { icon: <FileAudio className="h-5 w-5 text-green-600" />, title: "Audio Upload", description: "With text field" },
    { icon: <FileQuestion className="h-5 w-5 text-orange-600" />, title: "Option", description: "Multiple choice" },
    { icon: <ChevronDown className="h-5 w-5 text-blue-600" />, title: "Dropdown", description: "Select from list" },
    { icon: <PenTool className="h-5 w-5 text-indigo-600" />, title: "Sign", description: "Signature pad" },
    { icon: <CheckSquare className="h-5 w-5 text-green-600" />, title: "Checkbox", description: "Toggle options" },
    { icon: <Star className="h-5 w-5 text-yellow-600" />, title: "Star Rating", description: "1-5 stars" },
    { icon: <Calendar className="h-5 w-5 text-red-600" />, title: "Date", description: "Date selector" },
    { icon: <Clock className="h-5 w-5 text-purple-600" />, title: "Time", description: "From-To time selection" },
    { icon: <Timer className="h-5 w-5 text-blue-600" />, title: "Duration", description: "Time duration" },
    { icon: <Phone className="h-5 w-5 text-green-600" />, title: "Phone Number", description: "With formatting" },
    { icon: <Hash className="h-5 w-5 text-gray-600" />, title: "Alphanumeric", description: "Restricted input" },
  ];

  const componentsToShow = type === "content" ? contentComponents : formComponents;

  return (
    <div className={isCompact ? "p-1" : "p-3"}>
      {!isCompact && (
        <div className="text-xs text-gray-500 mb-3 px-2">Drag and drop to add</div>
      )}
      <motion.div
        className="space-y-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ staggerChildren: 0.05 }}
      >
        {componentsToShow.map((component, index) => {
          const componentId = `${type}-${component.title.toLowerCase().replace(/\s+/g, '-')}-${index}`;
          const componentWithId = {
            ...component,
            id: componentId,
            type: type === "content" ? "content" : "form"
          };

          return (
            <motion.div
              key={componentId}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2 }}
            >
              <DraggableComponent
                id={componentId}
                data={componentWithId}
                className={`border rounded-md ${isCompact ? 'p-2' : 'p-3'} flex items-center gap-3 hover:bg-gray-50 bg-white`}
              >
                <div className="flex items-center gap-3 w-full">
                  <div className="flex-shrink-0 p-1 rounded-md bg-gray-50">
                    {component.icon}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium">{component.title}</div>
                    {!isCompact && <div className="text-xs text-gray-500">{component.description}</div>}
                  </div>
                  {onAddComponent && isCompact && (
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-auto bg-gray-50"
                        onClick={() => onAddComponent(componentWithId)}
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </motion.div>
                  )}
                </div>
              </DraggableComponent>
            </motion.div>
          );
        })}
      </motion.div>
    </div>
  );
}
